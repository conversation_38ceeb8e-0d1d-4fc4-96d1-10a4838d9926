package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0084\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\b\u0007\u0018\u00002\u00020\u0001B\'\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ&\u0010\u001b\u001a\u00020\u001c2\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00110\u00102\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020!H\u0002J\u0006\u0010\"\u001a\u00020#J\u001e\u0010$\u001a\u00020#2\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020!2\u0006\u0010%\u001a\u00020&J,\u0010\'\u001a\b\u0012\u0004\u0012\u00020(0\u00102\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00110\u00102\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010)\u001a\u00020*H\u0002J,\u0010+\u001a\b\u0012\u0004\u0012\u00020,0\u00102\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00110\u00102\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020!H\u0002J\u0010\u0010-\u001a\u00020#2\b\b\u0002\u0010 \u001a\u00020!J\b\u0010.\u001a\u00020#H\u0002J\u000e\u0010/\u001a\u00020#2\u0006\u0010\u001e\u001a\u00020\u001fJ\u000e\u00100\u001a\u00020#2\u0006\u00101\u001a\u00020!R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u00100\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0014\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00150\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0013R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\r0\u0018\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00062"}, d2 = {"Lcom/focusflow/ui/viewmodel/PayoffPlannerViewModel;", "Landroidx/lifecycle/ViewModel;", "creditCardRepository", "Lcom/focusflow/data/repository/CreditCardRepository;", "payoffPlanRepository", "Lcom/focusflow/data/repository/PayoffPlanRepository;", "budgetCategoryRepository", "Lcom/focusflow/data/repository/BudgetCategoryRepository;", "userPreferencesRepository", "Lcom/focusflow/data/repository/UserPreferencesRepository;", "(Lcom/focusflow/data/repository/CreditCardRepository;Lcom/focusflow/data/repository/PayoffPlanRepository;Lcom/focusflow/data/repository/BudgetCategoryRepository;Lcom/focusflow/data/repository/UserPreferencesRepository;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/focusflow/ui/viewmodel/PayoffPlannerUiState;", "allCreditCards", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/focusflow/data/model/CreditCard;", "getAllCreditCards", "()Lkotlinx/coroutines/flow/Flow;", "currentPayoffPlan", "Lcom/focusflow/data/model/PayoffPlan;", "getCurrentPayoffPlan", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "calculatePayoffStrategy", "Lcom/focusflow/data/model/PayoffComparison;", "cards", "strategy", "Lcom/focusflow/ui/viewmodel/PayoffStrategy;", "extraPayment", "", "clearError", "", "createPayoffPlan", "planName", "", "generateMilestones", "Lcom/focusflow/data/model/PayoffMilestone;", "totalMonths", "", "generatePaymentSchedules", "Lcom/focusflow/data/model/PaymentSchedule;", "generatePayoffComparison", "loadPayoffPlannerData", "selectStrategy", "updateExtraPayment", "amount", "app_release"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class PayoffPlannerViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.CreditCardRepository creditCardRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.PayoffPlanRepository payoffPlanRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.BudgetCategoryRepository budgetCategoryRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.ui.viewmodel.PayoffPlannerUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.PayoffPlannerUiState> uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.CreditCard>> allCreditCards = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.Flow<com.focusflow.data.model.PayoffPlan> currentPayoffPlan = null;
    
    @javax.inject.Inject
    public PayoffPlannerViewModel(@org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.CreditCardRepository creditCardRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.PayoffPlanRepository payoffPlanRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.BudgetCategoryRepository budgetCategoryRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.PayoffPlannerUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.CreditCard>> getAllCreditCards() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<com.focusflow.data.model.PayoffPlan> getCurrentPayoffPlan() {
        return null;
    }
    
    private final void loadPayoffPlannerData() {
    }
    
    public final void generatePayoffComparison(double extraPayment) {
    }
    
    private final com.focusflow.data.model.PayoffComparison calculatePayoffStrategy(java.util.List<com.focusflow.data.model.CreditCard> cards, com.focusflow.ui.viewmodel.PayoffStrategy strategy, double extraPayment) {
        return null;
    }
    
    public final void createPayoffPlan(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffStrategy strategy, double extraPayment, @org.jetbrains.annotations.NotNull
    java.lang.String planName) {
    }
    
    private final java.util.List<com.focusflow.data.model.PaymentSchedule> generatePaymentSchedules(java.util.List<com.focusflow.data.model.CreditCard> cards, com.focusflow.ui.viewmodel.PayoffStrategy strategy, double extraPayment) {
        return null;
    }
    
    private final java.util.List<com.focusflow.data.model.PayoffMilestone> generateMilestones(java.util.List<com.focusflow.data.model.CreditCard> cards, com.focusflow.ui.viewmodel.PayoffStrategy strategy, int totalMonths) {
        return null;
    }
    
    public final void updateExtraPayment(double amount) {
    }
    
    public final void selectStrategy(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffStrategy strategy) {
    }
    
    public final void clearError() {
    }
}