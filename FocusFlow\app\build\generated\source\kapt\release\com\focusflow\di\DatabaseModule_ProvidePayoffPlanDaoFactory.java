package com.focusflow.di;

import com.focusflow.data.dao.PayoffPlanDao;
import com.focusflow.data.database.FocusFlowDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvidePayoffPlanDaoFactory implements Factory<PayoffPlanDao> {
  private final Provider<FocusFlowDatabase> databaseProvider;

  public DatabaseModule_ProvidePayoffPlanDaoFactory(Provider<FocusFlowDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public PayoffPlanDao get() {
    return providePayoffPlanDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvidePayoffPlanDaoFactory create(
      Provider<FocusFlowDatabase> databaseProvider) {
    return new DatabaseModule_ProvidePayoffPlanDaoFactory(databaseProvider);
  }

  public static PayoffPlanDao providePayoffPlanDao(FocusFlowDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.providePayoffPlanDao(database));
  }
}
