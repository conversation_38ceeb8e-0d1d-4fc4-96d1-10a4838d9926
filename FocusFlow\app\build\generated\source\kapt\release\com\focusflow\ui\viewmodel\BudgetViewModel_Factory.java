package com.focusflow.ui.viewmodel;

import com.focusflow.data.repository.BudgetCategoryRepository;
import com.focusflow.data.repository.ExpenseRepository;
import com.focusflow.data.repository.UserPreferencesRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class BudgetViewModel_Factory implements Factory<BudgetViewModel> {
  private final Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider;

  private final Provider<ExpenseRepository> expenseRepositoryProvider;

  private final Provider<UserPreferencesRepository> userPreferencesRepositoryProvider;

  public BudgetViewModel_Factory(
      Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider,
      Provider<ExpenseRepository> expenseRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider) {
    this.budgetCategoryRepositoryProvider = budgetCategoryRepositoryProvider;
    this.expenseRepositoryProvider = expenseRepositoryProvider;
    this.userPreferencesRepositoryProvider = userPreferencesRepositoryProvider;
  }

  @Override
  public BudgetViewModel get() {
    return newInstance(budgetCategoryRepositoryProvider.get(), expenseRepositoryProvider.get(), userPreferencesRepositoryProvider.get());
  }

  public static BudgetViewModel_Factory create(
      Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider,
      Provider<ExpenseRepository> expenseRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider) {
    return new BudgetViewModel_Factory(budgetCategoryRepositoryProvider, expenseRepositoryProvider, userPreferencesRepositoryProvider);
  }

  public static BudgetViewModel newInstance(BudgetCategoryRepository budgetCategoryRepository,
      ExpenseRepository expenseRepository, UserPreferencesRepository userPreferencesRepository) {
    return new BudgetViewModel(budgetCategoryRepository, expenseRepository, userPreferencesRepository);
  }
}
