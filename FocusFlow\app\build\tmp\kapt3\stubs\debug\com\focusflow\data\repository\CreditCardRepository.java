package com.focusflow.data.repository;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\u0005\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u0016\u0010\n\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\rJ\u0012\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u00100\u000fJ\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\f0\u0010H\u0086@\u00a2\u0006\u0002\u0010\u0012J\u001a\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u00100\u000f2\u0006\u0010\u0014\u001a\u00020\u0015J\u0018\u0010\u0016\u001a\u0004\u0018\u00010\f2\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u000e\u0010\u0017\u001a\u00020\u0018H\u0086@\u00a2\u0006\u0002\u0010\u0012J\u0016\u0010\u0019\u001a\u00020\u00182\u0006\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u001aJ\u0016\u0010\u001b\u001a\u00020\b2\u0006\u0010\u000b\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\rJ\u0016\u0010\u001c\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\rR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001d"}, d2 = {"Lcom/focusflow/data/repository/CreditCardRepository;", "", "creditCardDao", "Lcom/focusflow/data/dao/CreditCardDao;", "(Lcom/focusflow/data/dao/CreditCardDao;)V", "deactivateCreditCard", "", "cardId", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteCreditCard", "creditCard", "Lcom/focusflow/data/model/CreditCard;", "(Lcom/focusflow/data/model/CreditCard;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllActiveCreditCards", "Lkotlinx/coroutines/flow/Flow;", "", "getAllCreditCardsSync", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCardsWithPaymentsDue", "date", "Lkotlinx/datetime/LocalDate;", "getCreditCardById", "getTotalDebt", "", "getTotalMinimumPaymentsDue", "(Lkotlinx/datetime/LocalDate;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertCreditCard", "updateCreditCard", "app_debug"})
public final class CreditCardRepository {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.dao.CreditCardDao creditCardDao = null;
    
    @javax.inject.Inject
    public CreditCardRepository(@org.jetbrains.annotations.NotNull
    com.focusflow.data.dao.CreditCardDao creditCardDao) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.CreditCard>> getAllActiveCreditCards() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getCreditCardById(long cardId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.CreditCard> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getTotalDebt(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getTotalMinimumPaymentsDue(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate date, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.CreditCard>> getCardsWithPaymentsDue(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate date) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object insertCreditCard(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.CreditCard creditCard, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object updateCreditCard(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.CreditCard creditCard, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deleteCreditCard(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.CreditCard creditCard, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deactivateCreditCard(long cardId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getAllCreditCardsSync(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.CreditCard>> $completion) {
        return null;
    }
}