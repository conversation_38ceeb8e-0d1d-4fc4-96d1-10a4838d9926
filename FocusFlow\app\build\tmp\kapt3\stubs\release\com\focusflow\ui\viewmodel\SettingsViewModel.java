package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\b\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0002\b\u0005\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010\u000e\u001a\u00020\u000fJ\u0006\u0010\u0010\u001a\u00020\u000fJ\u0006\u0010\u0011\u001a\u00020\u000fJ\u0006\u0010\u0012\u001a\u00020\u000fJ\u0006\u0010\u0013\u001a\u00020\u000fJ\u0006\u0010\u0014\u001a\u00020\u000fJ\b\u0010\u0015\u001a\u00020\u000fH\u0002J\u000e\u0010\u0016\u001a\u00020\u000f2\u0006\u0010\u0017\u001a\u00020\u0018J\u000e\u0010\u0019\u001a\u00020\u000f2\u0006\u0010\u001a\u001a\u00020\u0018J\u000e\u0010\u001b\u001a\u00020\u000f2\u0006\u0010\u001c\u001a\u00020\u0018J\u000e\u0010\u001d\u001a\u00020\u000f2\u0006\u0010\u001e\u001a\u00020\u001fJ\u0010\u0010 \u001a\u00020\u000f2\b\u0010!\u001a\u0004\u0018\u00010\u0018J\u000e\u0010\"\u001a\u00020\u000f2\u0006\u0010#\u001a\u00020\u0018R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006$"}, d2 = {"Lcom/focusflow/ui/viewmodel/SettingsViewModel;", "Landroidx/lifecycle/ViewModel;", "userPreferencesRepository", "Lcom/focusflow/data/repository/UserPreferencesRepository;", "notificationRepository", "Lcom/focusflow/data/repository/NotificationRepository;", "(Lcom/focusflow/data/repository/UserPreferencesRepository;Lcom/focusflow/data/repository/NotificationRepository;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/focusflow/ui/viewmodel/SettingsUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "checkNotificationPermission", "", "clearData", "clearError", "clearMessages", "exportData", "initializeNotifications", "loadUserPreferences", "updateBudgetPeriod", "period", "", "updateFontSize", "fontSize", "updateNotificationTime", "time", "updateNotificationsEnabled", "enabled", "", "updatePrimaryGoal", "goal", "updateTheme", "theme", "app_release"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class SettingsViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.NotificationRepository notificationRepository = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.ui.viewmodel.SettingsUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.SettingsUiState> uiState = null;
    
    @javax.inject.Inject
    public SettingsViewModel(@org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.NotificationRepository notificationRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.SettingsUiState> getUiState() {
        return null;
    }
    
    private final void loadUserPreferences() {
    }
    
    public final void updateTheme(@org.jetbrains.annotations.NotNull
    java.lang.String theme) {
    }
    
    public final void updateFontSize(@org.jetbrains.annotations.NotNull
    java.lang.String fontSize) {
    }
    
    public final void updateNotificationsEnabled(boolean enabled) {
    }
    
    public final void updateNotificationTime(@org.jetbrains.annotations.NotNull
    java.lang.String time) {
    }
    
    public final void updateBudgetPeriod(@org.jetbrains.annotations.NotNull
    java.lang.String period) {
    }
    
    public final void updatePrimaryGoal(@org.jetbrains.annotations.Nullable
    java.lang.String goal) {
    }
    
    public final void exportData() {
    }
    
    public final void clearData() {
    }
    
    public final void clearError() {
    }
    
    public final void clearMessages() {
    }
    
    public final void checkNotificationPermission() {
    }
    
    public final void initializeNotifications() {
    }
}