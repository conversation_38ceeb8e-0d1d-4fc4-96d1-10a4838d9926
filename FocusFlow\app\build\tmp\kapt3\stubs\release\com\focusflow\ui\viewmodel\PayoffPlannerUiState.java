package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\"\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u0085\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0003\u0012\b\b\u0002\u0010\b\u001a\u00020\u0003\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\f\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u000f\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u000f\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u000f\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0002\u0010\u0013J\t\u0010\"\u001a\u00020\u0003H\u00c6\u0003J\t\u0010#\u001a\u00020\u000fH\u00c6\u0003J\t\u0010$\u001a\u00020\u000fH\u00c6\u0003J\u000b\u0010%\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\t\u0010&\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\'\u001a\u00020\u0006H\u00c6\u0003J\t\u0010(\u001a\u00020\u0003H\u00c6\u0003J\t\u0010)\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010*\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000b\u0010+\u001a\u0004\u0018\u00010\fH\u00c6\u0003J\u000b\u0010,\u001a\u0004\u0018\u00010\fH\u00c6\u0003J\t\u0010-\u001a\u00020\u000fH\u00c6\u0003J\u0089\u0001\u0010.\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\f2\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0010\u001a\u00020\u000f2\b\b\u0002\u0010\u0011\u001a\u00020\u000f2\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001J\u0013\u0010/\u001a\u00020\u000f2\b\u00100\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00101\u001a\u000202H\u00d6\u0001J\t\u00103\u001a\u00020\u0006H\u00d6\u0001R\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0019R\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0015R\u0011\u0010\u0011\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u001cR\u0011\u0010\u0010\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u001cR\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u001cR\u0013\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001eR\u0013\u0010\r\u001a\u0004\u0018\u00010\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0017R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0015R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0015\u00a8\u00064"}, d2 = {"Lcom/focusflow/ui/viewmodel/PayoffPlannerUiState;", "", "totalDebt", "", "totalMinimumPayments", "budgetPeriod", "", "availableForDebtPayment", "extraPaymentAmount", "selectedStrategy", "Lcom/focusflow/ui/viewmodel/PayoffStrategy;", "avalancheComparison", "Lcom/focusflow/data/model/PayoffComparison;", "snowballComparison", "isLoading", "", "isGeneratingPlan", "isCreatingPlan", "error", "(DDLjava/lang/String;DDLcom/focusflow/ui/viewmodel/PayoffStrategy;Lcom/focusflow/data/model/PayoffComparison;Lcom/focusflow/data/model/PayoffComparison;ZZZLjava/lang/String;)V", "getAvailableForDebtPayment", "()D", "getAvalancheComparison", "()Lcom/focusflow/data/model/PayoffComparison;", "getBudgetPeriod", "()Ljava/lang/String;", "getError", "getExtraPaymentAmount", "()Z", "getSelectedStrategy", "()Lcom/focusflow/ui/viewmodel/PayoffStrategy;", "getSnowballComparison", "getTotalDebt", "getTotalMinimumPayments", "component1", "component10", "component11", "component12", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toString", "app_release"})
public final class PayoffPlannerUiState {
    private final double totalDebt = 0.0;
    private final double totalMinimumPayments = 0.0;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String budgetPeriod = null;
    private final double availableForDebtPayment = 0.0;
    private final double extraPaymentAmount = 0.0;
    @org.jetbrains.annotations.Nullable
    private final com.focusflow.ui.viewmodel.PayoffStrategy selectedStrategy = null;
    @org.jetbrains.annotations.Nullable
    private final com.focusflow.data.model.PayoffComparison avalancheComparison = null;
    @org.jetbrains.annotations.Nullable
    private final com.focusflow.data.model.PayoffComparison snowballComparison = null;
    private final boolean isLoading = false;
    private final boolean isGeneratingPlan = false;
    private final boolean isCreatingPlan = false;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String error = null;
    
    public PayoffPlannerUiState(double totalDebt, double totalMinimumPayments, @org.jetbrains.annotations.NotNull
    java.lang.String budgetPeriod, double availableForDebtPayment, double extraPaymentAmount, @org.jetbrains.annotations.Nullable
    com.focusflow.ui.viewmodel.PayoffStrategy selectedStrategy, @org.jetbrains.annotations.Nullable
    com.focusflow.data.model.PayoffComparison avalancheComparison, @org.jetbrains.annotations.Nullable
    com.focusflow.data.model.PayoffComparison snowballComparison, boolean isLoading, boolean isGeneratingPlan, boolean isCreatingPlan, @org.jetbrains.annotations.Nullable
    java.lang.String error) {
        super();
    }
    
    public final double getTotalDebt() {
        return 0.0;
    }
    
    public final double getTotalMinimumPayments() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getBudgetPeriod() {
        return null;
    }
    
    public final double getAvailableForDebtPayment() {
        return 0.0;
    }
    
    public final double getExtraPaymentAmount() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.ui.viewmodel.PayoffStrategy getSelectedStrategy() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.data.model.PayoffComparison getAvalancheComparison() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.data.model.PayoffComparison getSnowballComparison() {
        return null;
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    public final boolean isGeneratingPlan() {
        return false;
    }
    
    public final boolean isCreatingPlan() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getError() {
        return null;
    }
    
    public PayoffPlannerUiState() {
        super();
    }
    
    public final double component1() {
        return 0.0;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final boolean component11() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component12() {
        return null;
    }
    
    public final double component2() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component3() {
        return null;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    public final double component5() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.ui.viewmodel.PayoffStrategy component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.data.model.PayoffComparison component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.data.model.PayoffComparison component8() {
        return null;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.ui.viewmodel.PayoffPlannerUiState copy(double totalDebt, double totalMinimumPayments, @org.jetbrains.annotations.NotNull
    java.lang.String budgetPeriod, double availableForDebtPayment, double extraPaymentAmount, @org.jetbrains.annotations.Nullable
    com.focusflow.ui.viewmodel.PayoffStrategy selectedStrategy, @org.jetbrains.annotations.Nullable
    com.focusflow.data.model.PayoffComparison avalancheComparison, @org.jetbrains.annotations.Nullable
    com.focusflow.data.model.PayoffComparison snowballComparison, boolean isLoading, boolean isGeneratingPlan, boolean isCreatingPlan, @org.jetbrains.annotations.Nullable
    java.lang.String error) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}