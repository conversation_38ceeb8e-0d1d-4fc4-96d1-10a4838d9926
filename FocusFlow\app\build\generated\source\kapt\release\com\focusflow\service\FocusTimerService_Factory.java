package com.focusflow.service;

import com.focusflow.data.dao.FocusSessionDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FocusTimerService_Factory implements Factory<FocusTimerService> {
  private final Provider<FocusSessionDao> focusSessionDaoProvider;

  private final Provider<NotificationService> notificationServiceProvider;

  private final Provider<GamificationService> gamificationServiceProvider;

  public FocusTimerService_Factory(Provider<FocusSessionDao> focusSessionDaoProvider,
      Provider<NotificationService> notificationServiceProvider,
      Provider<GamificationService> gamificationServiceProvider) {
    this.focusSessionDaoProvider = focusSessionDaoProvider;
    this.notificationServiceProvider = notificationServiceProvider;
    this.gamificationServiceProvider = gamificationServiceProvider;
  }

  @Override
  public FocusTimerService get() {
    return newInstance(focusSessionDaoProvider.get(), notificationServiceProvider.get(), gamificationServiceProvider.get());
  }

  public static FocusTimerService_Factory create(Provider<FocusSessionDao> focusSessionDaoProvider,
      Provider<NotificationService> notificationServiceProvider,
      Provider<GamificationService> gamificationServiceProvider) {
    return new FocusTimerService_Factory(focusSessionDaoProvider, notificationServiceProvider, gamificationServiceProvider);
  }

  public static FocusTimerService newInstance(FocusSessionDao focusSessionDao,
      NotificationService notificationService, GamificationService gamificationService) {
    return new FocusTimerService(focusSessionDao, notificationService, gamificationService);
  }
}
