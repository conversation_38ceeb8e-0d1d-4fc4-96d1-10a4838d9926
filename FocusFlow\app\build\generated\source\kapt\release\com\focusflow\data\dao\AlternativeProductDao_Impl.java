package com.focusflow.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.focusflow.data.database.Converters;
import com.focusflow.data.model.AlternativeProduct;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;
import kotlinx.datetime.LocalDateTime;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AlternativeProductDao_Impl implements AlternativeProductDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<AlternativeProduct> __insertionAdapterOfAlternativeProduct;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<AlternativeProduct> __deletionAdapterOfAlternativeProduct;

  private final EntityDeletionOrUpdateAdapter<AlternativeProduct> __updateAdapterOfAlternativeProduct;

  private final SharedSQLiteStatement __preparedStmtOfRecordAlternativeShown;

  private final SharedSQLiteStatement __preparedStmtOfRecordAlternativeAccepted;

  private final SharedSQLiteStatement __preparedStmtOfRecordAlternativeRejected;

  private final SharedSQLiteStatement __preparedStmtOfUpdateUserFeedback;

  private final SharedSQLiteStatement __preparedStmtOfDeactivateAlternative;

  private final SharedSQLiteStatement __preparedStmtOfDeleteUnusedOldAlternatives;

  public AlternativeProductDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfAlternativeProduct = new EntityInsertionAdapter<AlternativeProduct>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `alternative_products` (`id`,`originalProductName`,`originalPrice`,`originalCategory`,`alternativeName`,`alternativePrice`,`alternativeCategory`,`alternativeType`,`savingsAmount`,`savingsPercentage`,`description`,`pros`,`cons`,`availabilityInfo`,`qualityRating`,`userRating`,`suggestionSource`,`confidenceScore`,`createdDate`,`lastSuggested`,`timesShown`,`timesAccepted`,`timesRejected`,`userFeedback`,`isActive`,`tags`,`imageUrl`,`productUrl`,`merchant`,`estimatedDeliveryTime`,`sustainabilityScore`,`difficultyLevel`,`timeInvestment`,`requiredSkills`,`successRate`,`relatedAlternatives`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final AlternativeProduct entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getOriginalProductName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getOriginalProductName());
        }
        statement.bindDouble(3, entity.getOriginalPrice());
        if (entity.getOriginalCategory() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getOriginalCategory());
        }
        if (entity.getAlternativeName() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getAlternativeName());
        }
        statement.bindDouble(6, entity.getAlternativePrice());
        if (entity.getAlternativeCategory() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getAlternativeCategory());
        }
        if (entity.getAlternativeType() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getAlternativeType());
        }
        statement.bindDouble(9, entity.getSavingsAmount());
        statement.bindDouble(10, entity.getSavingsPercentage());
        if (entity.getDescription() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getDescription());
        }
        if (entity.getPros() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getPros());
        }
        if (entity.getCons() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getCons());
        }
        if (entity.getAvailabilityInfo() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getAvailabilityInfo());
        }
        if (entity.getQualityRating() == null) {
          statement.bindNull(15);
        } else {
          statement.bindLong(15, entity.getQualityRating());
        }
        if (entity.getUserRating() == null) {
          statement.bindNull(16);
        } else {
          statement.bindLong(16, entity.getUserRating());
        }
        if (entity.getSuggestionSource() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getSuggestionSource());
        }
        statement.bindDouble(18, entity.getConfidenceScore());
        final String _tmp = __converters.fromLocalDateTime(entity.getCreatedDate());
        if (_tmp == null) {
          statement.bindNull(19);
        } else {
          statement.bindString(19, _tmp);
        }
        final String _tmp_1 = __converters.fromLocalDateTime(entity.getLastSuggested());
        if (_tmp_1 == null) {
          statement.bindNull(20);
        } else {
          statement.bindString(20, _tmp_1);
        }
        statement.bindLong(21, entity.getTimesShown());
        statement.bindLong(22, entity.getTimesAccepted());
        statement.bindLong(23, entity.getTimesRejected());
        if (entity.getUserFeedback() == null) {
          statement.bindNull(24);
        } else {
          statement.bindString(24, entity.getUserFeedback());
        }
        final int _tmp_2 = entity.isActive() ? 1 : 0;
        statement.bindLong(25, _tmp_2);
        if (entity.getTags() == null) {
          statement.bindNull(26);
        } else {
          statement.bindString(26, entity.getTags());
        }
        if (entity.getImageUrl() == null) {
          statement.bindNull(27);
        } else {
          statement.bindString(27, entity.getImageUrl());
        }
        if (entity.getProductUrl() == null) {
          statement.bindNull(28);
        } else {
          statement.bindString(28, entity.getProductUrl());
        }
        if (entity.getMerchant() == null) {
          statement.bindNull(29);
        } else {
          statement.bindString(29, entity.getMerchant());
        }
        if (entity.getEstimatedDeliveryTime() == null) {
          statement.bindNull(30);
        } else {
          statement.bindString(30, entity.getEstimatedDeliveryTime());
        }
        if (entity.getSustainabilityScore() == null) {
          statement.bindNull(31);
        } else {
          statement.bindLong(31, entity.getSustainabilityScore());
        }
        if (entity.getDifficultyLevel() == null) {
          statement.bindNull(32);
        } else {
          statement.bindString(32, entity.getDifficultyLevel());
        }
        if (entity.getTimeInvestment() == null) {
          statement.bindNull(33);
        } else {
          statement.bindString(33, entity.getTimeInvestment());
        }
        if (entity.getRequiredSkills() == null) {
          statement.bindNull(34);
        } else {
          statement.bindString(34, entity.getRequiredSkills());
        }
        if (entity.getSuccessRate() == null) {
          statement.bindNull(35);
        } else {
          statement.bindDouble(35, entity.getSuccessRate());
        }
        if (entity.getRelatedAlternatives() == null) {
          statement.bindNull(36);
        } else {
          statement.bindString(36, entity.getRelatedAlternatives());
        }
      }
    };
    this.__deletionAdapterOfAlternativeProduct = new EntityDeletionOrUpdateAdapter<AlternativeProduct>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `alternative_products` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final AlternativeProduct entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfAlternativeProduct = new EntityDeletionOrUpdateAdapter<AlternativeProduct>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `alternative_products` SET `id` = ?,`originalProductName` = ?,`originalPrice` = ?,`originalCategory` = ?,`alternativeName` = ?,`alternativePrice` = ?,`alternativeCategory` = ?,`alternativeType` = ?,`savingsAmount` = ?,`savingsPercentage` = ?,`description` = ?,`pros` = ?,`cons` = ?,`availabilityInfo` = ?,`qualityRating` = ?,`userRating` = ?,`suggestionSource` = ?,`confidenceScore` = ?,`createdDate` = ?,`lastSuggested` = ?,`timesShown` = ?,`timesAccepted` = ?,`timesRejected` = ?,`userFeedback` = ?,`isActive` = ?,`tags` = ?,`imageUrl` = ?,`productUrl` = ?,`merchant` = ?,`estimatedDeliveryTime` = ?,`sustainabilityScore` = ?,`difficultyLevel` = ?,`timeInvestment` = ?,`requiredSkills` = ?,`successRate` = ?,`relatedAlternatives` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final AlternativeProduct entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getOriginalProductName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getOriginalProductName());
        }
        statement.bindDouble(3, entity.getOriginalPrice());
        if (entity.getOriginalCategory() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getOriginalCategory());
        }
        if (entity.getAlternativeName() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getAlternativeName());
        }
        statement.bindDouble(6, entity.getAlternativePrice());
        if (entity.getAlternativeCategory() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getAlternativeCategory());
        }
        if (entity.getAlternativeType() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getAlternativeType());
        }
        statement.bindDouble(9, entity.getSavingsAmount());
        statement.bindDouble(10, entity.getSavingsPercentage());
        if (entity.getDescription() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getDescription());
        }
        if (entity.getPros() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getPros());
        }
        if (entity.getCons() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getCons());
        }
        if (entity.getAvailabilityInfo() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getAvailabilityInfo());
        }
        if (entity.getQualityRating() == null) {
          statement.bindNull(15);
        } else {
          statement.bindLong(15, entity.getQualityRating());
        }
        if (entity.getUserRating() == null) {
          statement.bindNull(16);
        } else {
          statement.bindLong(16, entity.getUserRating());
        }
        if (entity.getSuggestionSource() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getSuggestionSource());
        }
        statement.bindDouble(18, entity.getConfidenceScore());
        final String _tmp = __converters.fromLocalDateTime(entity.getCreatedDate());
        if (_tmp == null) {
          statement.bindNull(19);
        } else {
          statement.bindString(19, _tmp);
        }
        final String _tmp_1 = __converters.fromLocalDateTime(entity.getLastSuggested());
        if (_tmp_1 == null) {
          statement.bindNull(20);
        } else {
          statement.bindString(20, _tmp_1);
        }
        statement.bindLong(21, entity.getTimesShown());
        statement.bindLong(22, entity.getTimesAccepted());
        statement.bindLong(23, entity.getTimesRejected());
        if (entity.getUserFeedback() == null) {
          statement.bindNull(24);
        } else {
          statement.bindString(24, entity.getUserFeedback());
        }
        final int _tmp_2 = entity.isActive() ? 1 : 0;
        statement.bindLong(25, _tmp_2);
        if (entity.getTags() == null) {
          statement.bindNull(26);
        } else {
          statement.bindString(26, entity.getTags());
        }
        if (entity.getImageUrl() == null) {
          statement.bindNull(27);
        } else {
          statement.bindString(27, entity.getImageUrl());
        }
        if (entity.getProductUrl() == null) {
          statement.bindNull(28);
        } else {
          statement.bindString(28, entity.getProductUrl());
        }
        if (entity.getMerchant() == null) {
          statement.bindNull(29);
        } else {
          statement.bindString(29, entity.getMerchant());
        }
        if (entity.getEstimatedDeliveryTime() == null) {
          statement.bindNull(30);
        } else {
          statement.bindString(30, entity.getEstimatedDeliveryTime());
        }
        if (entity.getSustainabilityScore() == null) {
          statement.bindNull(31);
        } else {
          statement.bindLong(31, entity.getSustainabilityScore());
        }
        if (entity.getDifficultyLevel() == null) {
          statement.bindNull(32);
        } else {
          statement.bindString(32, entity.getDifficultyLevel());
        }
        if (entity.getTimeInvestment() == null) {
          statement.bindNull(33);
        } else {
          statement.bindString(33, entity.getTimeInvestment());
        }
        if (entity.getRequiredSkills() == null) {
          statement.bindNull(34);
        } else {
          statement.bindString(34, entity.getRequiredSkills());
        }
        if (entity.getSuccessRate() == null) {
          statement.bindNull(35);
        } else {
          statement.bindDouble(35, entity.getSuccessRate());
        }
        if (entity.getRelatedAlternatives() == null) {
          statement.bindNull(36);
        } else {
          statement.bindString(36, entity.getRelatedAlternatives());
        }
        statement.bindLong(37, entity.getId());
      }
    };
    this.__preparedStmtOfRecordAlternativeShown = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE alternative_products SET timesShown = timesShown + 1, lastSuggested = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfRecordAlternativeAccepted = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE alternative_products SET timesAccepted = timesAccepted + 1 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfRecordAlternativeRejected = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE alternative_products SET timesRejected = timesRejected + 1 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateUserFeedback = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE alternative_products SET userRating = ?, userFeedback = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeactivateAlternative = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE alternative_products SET isActive = 0 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteUnusedOldAlternatives = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM alternative_products WHERE createdDate < ? AND timesAccepted = 0";
        return _query;
      }
    };
  }

  @Override
  public Object insertAlternative(final AlternativeProduct alternative,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfAlternativeProduct.insertAndReturnId(alternative);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAlternative(final AlternativeProduct alternative,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfAlternativeProduct.handle(alternative);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateAlternative(final AlternativeProduct alternative,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfAlternativeProduct.handle(alternative);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object recordAlternativeShown(final long id, final LocalDateTime timestamp,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfRecordAlternativeShown.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromLocalDateTime(timestamp);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfRecordAlternativeShown.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object recordAlternativeAccepted(final long id,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfRecordAlternativeAccepted.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfRecordAlternativeAccepted.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object recordAlternativeRejected(final long id,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfRecordAlternativeRejected.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfRecordAlternativeRejected.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateUserFeedback(final long id, final Integer rating, final String feedback,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateUserFeedback.acquire();
        int _argIndex = 1;
        if (rating == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindLong(_argIndex, rating);
        }
        _argIndex = 2;
        if (feedback == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, feedback);
        }
        _argIndex = 3;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateUserFeedback.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deactivateAlternative(final long id, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeactivateAlternative.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeactivateAlternative.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteUnusedOldAlternatives(final LocalDateTime cutoffDate,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteUnusedOldAlternatives.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromLocalDateTime(cutoffDate);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteUnusedOldAlternatives.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<AlternativeProduct>> getAllActiveAlternatives() {
    final String _sql = "SELECT * FROM alternative_products WHERE isActive = 1 ORDER BY confidenceScore DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"alternative_products"}, new Callable<List<AlternativeProduct>>() {
      @Override
      @NonNull
      public List<AlternativeProduct> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfOriginalProductName = CursorUtil.getColumnIndexOrThrow(_cursor, "originalProductName");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfOriginalCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "originalCategory");
          final int _cursorIndexOfAlternativeName = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeName");
          final int _cursorIndexOfAlternativePrice = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativePrice");
          final int _cursorIndexOfAlternativeCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeCategory");
          final int _cursorIndexOfAlternativeType = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeType");
          final int _cursorIndexOfSavingsAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "savingsAmount");
          final int _cursorIndexOfSavingsPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "savingsPercentage");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfPros = CursorUtil.getColumnIndexOrThrow(_cursor, "pros");
          final int _cursorIndexOfCons = CursorUtil.getColumnIndexOrThrow(_cursor, "cons");
          final int _cursorIndexOfAvailabilityInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "availabilityInfo");
          final int _cursorIndexOfQualityRating = CursorUtil.getColumnIndexOrThrow(_cursor, "qualityRating");
          final int _cursorIndexOfUserRating = CursorUtil.getColumnIndexOrThrow(_cursor, "userRating");
          final int _cursorIndexOfSuggestionSource = CursorUtil.getColumnIndexOrThrow(_cursor, "suggestionSource");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfCreatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "createdDate");
          final int _cursorIndexOfLastSuggested = CursorUtil.getColumnIndexOrThrow(_cursor, "lastSuggested");
          final int _cursorIndexOfTimesShown = CursorUtil.getColumnIndexOrThrow(_cursor, "timesShown");
          final int _cursorIndexOfTimesAccepted = CursorUtil.getColumnIndexOrThrow(_cursor, "timesAccepted");
          final int _cursorIndexOfTimesRejected = CursorUtil.getColumnIndexOrThrow(_cursor, "timesRejected");
          final int _cursorIndexOfUserFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "userFeedback");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUrl");
          final int _cursorIndexOfProductUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "productUrl");
          final int _cursorIndexOfMerchant = CursorUtil.getColumnIndexOrThrow(_cursor, "merchant");
          final int _cursorIndexOfEstimatedDeliveryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedDeliveryTime");
          final int _cursorIndexOfSustainabilityScore = CursorUtil.getColumnIndexOrThrow(_cursor, "sustainabilityScore");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfTimeInvestment = CursorUtil.getColumnIndexOrThrow(_cursor, "timeInvestment");
          final int _cursorIndexOfRequiredSkills = CursorUtil.getColumnIndexOrThrow(_cursor, "requiredSkills");
          final int _cursorIndexOfSuccessRate = CursorUtil.getColumnIndexOrThrow(_cursor, "successRate");
          final int _cursorIndexOfRelatedAlternatives = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedAlternatives");
          final List<AlternativeProduct> _result = new ArrayList<AlternativeProduct>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AlternativeProduct _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpOriginalProductName;
            if (_cursor.isNull(_cursorIndexOfOriginalProductName)) {
              _tmpOriginalProductName = null;
            } else {
              _tmpOriginalProductName = _cursor.getString(_cursorIndexOfOriginalProductName);
            }
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final String _tmpOriginalCategory;
            if (_cursor.isNull(_cursorIndexOfOriginalCategory)) {
              _tmpOriginalCategory = null;
            } else {
              _tmpOriginalCategory = _cursor.getString(_cursorIndexOfOriginalCategory);
            }
            final String _tmpAlternativeName;
            if (_cursor.isNull(_cursorIndexOfAlternativeName)) {
              _tmpAlternativeName = null;
            } else {
              _tmpAlternativeName = _cursor.getString(_cursorIndexOfAlternativeName);
            }
            final double _tmpAlternativePrice;
            _tmpAlternativePrice = _cursor.getDouble(_cursorIndexOfAlternativePrice);
            final String _tmpAlternativeCategory;
            if (_cursor.isNull(_cursorIndexOfAlternativeCategory)) {
              _tmpAlternativeCategory = null;
            } else {
              _tmpAlternativeCategory = _cursor.getString(_cursorIndexOfAlternativeCategory);
            }
            final String _tmpAlternativeType;
            if (_cursor.isNull(_cursorIndexOfAlternativeType)) {
              _tmpAlternativeType = null;
            } else {
              _tmpAlternativeType = _cursor.getString(_cursorIndexOfAlternativeType);
            }
            final double _tmpSavingsAmount;
            _tmpSavingsAmount = _cursor.getDouble(_cursorIndexOfSavingsAmount);
            final double _tmpSavingsPercentage;
            _tmpSavingsPercentage = _cursor.getDouble(_cursorIndexOfSavingsPercentage);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpPros;
            if (_cursor.isNull(_cursorIndexOfPros)) {
              _tmpPros = null;
            } else {
              _tmpPros = _cursor.getString(_cursorIndexOfPros);
            }
            final String _tmpCons;
            if (_cursor.isNull(_cursorIndexOfCons)) {
              _tmpCons = null;
            } else {
              _tmpCons = _cursor.getString(_cursorIndexOfCons);
            }
            final String _tmpAvailabilityInfo;
            if (_cursor.isNull(_cursorIndexOfAvailabilityInfo)) {
              _tmpAvailabilityInfo = null;
            } else {
              _tmpAvailabilityInfo = _cursor.getString(_cursorIndexOfAvailabilityInfo);
            }
            final Integer _tmpQualityRating;
            if (_cursor.isNull(_cursorIndexOfQualityRating)) {
              _tmpQualityRating = null;
            } else {
              _tmpQualityRating = _cursor.getInt(_cursorIndexOfQualityRating);
            }
            final Integer _tmpUserRating;
            if (_cursor.isNull(_cursorIndexOfUserRating)) {
              _tmpUserRating = null;
            } else {
              _tmpUserRating = _cursor.getInt(_cursorIndexOfUserRating);
            }
            final String _tmpSuggestionSource;
            if (_cursor.isNull(_cursorIndexOfSuggestionSource)) {
              _tmpSuggestionSource = null;
            } else {
              _tmpSuggestionSource = _cursor.getString(_cursorIndexOfSuggestionSource);
            }
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final LocalDateTime _tmpCreatedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCreatedDate);
            }
            _tmpCreatedDate = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpLastSuggested;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfLastSuggested)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfLastSuggested);
            }
            _tmpLastSuggested = __converters.toLocalDateTime(_tmp_1);
            final int _tmpTimesShown;
            _tmpTimesShown = _cursor.getInt(_cursorIndexOfTimesShown);
            final int _tmpTimesAccepted;
            _tmpTimesAccepted = _cursor.getInt(_cursorIndexOfTimesAccepted);
            final int _tmpTimesRejected;
            _tmpTimesRejected = _cursor.getInt(_cursorIndexOfTimesRejected);
            final String _tmpUserFeedback;
            if (_cursor.isNull(_cursorIndexOfUserFeedback)) {
              _tmpUserFeedback = null;
            } else {
              _tmpUserFeedback = _cursor.getString(_cursorIndexOfUserFeedback);
            }
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final String _tmpImageUrl;
            if (_cursor.isNull(_cursorIndexOfImageUrl)) {
              _tmpImageUrl = null;
            } else {
              _tmpImageUrl = _cursor.getString(_cursorIndexOfImageUrl);
            }
            final String _tmpProductUrl;
            if (_cursor.isNull(_cursorIndexOfProductUrl)) {
              _tmpProductUrl = null;
            } else {
              _tmpProductUrl = _cursor.getString(_cursorIndexOfProductUrl);
            }
            final String _tmpMerchant;
            if (_cursor.isNull(_cursorIndexOfMerchant)) {
              _tmpMerchant = null;
            } else {
              _tmpMerchant = _cursor.getString(_cursorIndexOfMerchant);
            }
            final String _tmpEstimatedDeliveryTime;
            if (_cursor.isNull(_cursorIndexOfEstimatedDeliveryTime)) {
              _tmpEstimatedDeliveryTime = null;
            } else {
              _tmpEstimatedDeliveryTime = _cursor.getString(_cursorIndexOfEstimatedDeliveryTime);
            }
            final Integer _tmpSustainabilityScore;
            if (_cursor.isNull(_cursorIndexOfSustainabilityScore)) {
              _tmpSustainabilityScore = null;
            } else {
              _tmpSustainabilityScore = _cursor.getInt(_cursorIndexOfSustainabilityScore);
            }
            final String _tmpDifficultyLevel;
            if (_cursor.isNull(_cursorIndexOfDifficultyLevel)) {
              _tmpDifficultyLevel = null;
            } else {
              _tmpDifficultyLevel = _cursor.getString(_cursorIndexOfDifficultyLevel);
            }
            final String _tmpTimeInvestment;
            if (_cursor.isNull(_cursorIndexOfTimeInvestment)) {
              _tmpTimeInvestment = null;
            } else {
              _tmpTimeInvestment = _cursor.getString(_cursorIndexOfTimeInvestment);
            }
            final String _tmpRequiredSkills;
            if (_cursor.isNull(_cursorIndexOfRequiredSkills)) {
              _tmpRequiredSkills = null;
            } else {
              _tmpRequiredSkills = _cursor.getString(_cursorIndexOfRequiredSkills);
            }
            final Double _tmpSuccessRate;
            if (_cursor.isNull(_cursorIndexOfSuccessRate)) {
              _tmpSuccessRate = null;
            } else {
              _tmpSuccessRate = _cursor.getDouble(_cursorIndexOfSuccessRate);
            }
            final String _tmpRelatedAlternatives;
            if (_cursor.isNull(_cursorIndexOfRelatedAlternatives)) {
              _tmpRelatedAlternatives = null;
            } else {
              _tmpRelatedAlternatives = _cursor.getString(_cursorIndexOfRelatedAlternatives);
            }
            _item = new AlternativeProduct(_tmpId,_tmpOriginalProductName,_tmpOriginalPrice,_tmpOriginalCategory,_tmpAlternativeName,_tmpAlternativePrice,_tmpAlternativeCategory,_tmpAlternativeType,_tmpSavingsAmount,_tmpSavingsPercentage,_tmpDescription,_tmpPros,_tmpCons,_tmpAvailabilityInfo,_tmpQualityRating,_tmpUserRating,_tmpSuggestionSource,_tmpConfidenceScore,_tmpCreatedDate,_tmpLastSuggested,_tmpTimesShown,_tmpTimesAccepted,_tmpTimesRejected,_tmpUserFeedback,_tmpIsActive,_tmpTags,_tmpImageUrl,_tmpProductUrl,_tmpMerchant,_tmpEstimatedDeliveryTime,_tmpSustainabilityScore,_tmpDifficultyLevel,_tmpTimeInvestment,_tmpRequiredSkills,_tmpSuccessRate,_tmpRelatedAlternatives);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<AlternativeProduct>> getAlternativesByCategory(final String category) {
    final String _sql = "SELECT * FROM alternative_products WHERE originalCategory = ? AND isActive = 1 ORDER BY savingsAmount DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"alternative_products"}, new Callable<List<AlternativeProduct>>() {
      @Override
      @NonNull
      public List<AlternativeProduct> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfOriginalProductName = CursorUtil.getColumnIndexOrThrow(_cursor, "originalProductName");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfOriginalCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "originalCategory");
          final int _cursorIndexOfAlternativeName = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeName");
          final int _cursorIndexOfAlternativePrice = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativePrice");
          final int _cursorIndexOfAlternativeCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeCategory");
          final int _cursorIndexOfAlternativeType = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeType");
          final int _cursorIndexOfSavingsAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "savingsAmount");
          final int _cursorIndexOfSavingsPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "savingsPercentage");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfPros = CursorUtil.getColumnIndexOrThrow(_cursor, "pros");
          final int _cursorIndexOfCons = CursorUtil.getColumnIndexOrThrow(_cursor, "cons");
          final int _cursorIndexOfAvailabilityInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "availabilityInfo");
          final int _cursorIndexOfQualityRating = CursorUtil.getColumnIndexOrThrow(_cursor, "qualityRating");
          final int _cursorIndexOfUserRating = CursorUtil.getColumnIndexOrThrow(_cursor, "userRating");
          final int _cursorIndexOfSuggestionSource = CursorUtil.getColumnIndexOrThrow(_cursor, "suggestionSource");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfCreatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "createdDate");
          final int _cursorIndexOfLastSuggested = CursorUtil.getColumnIndexOrThrow(_cursor, "lastSuggested");
          final int _cursorIndexOfTimesShown = CursorUtil.getColumnIndexOrThrow(_cursor, "timesShown");
          final int _cursorIndexOfTimesAccepted = CursorUtil.getColumnIndexOrThrow(_cursor, "timesAccepted");
          final int _cursorIndexOfTimesRejected = CursorUtil.getColumnIndexOrThrow(_cursor, "timesRejected");
          final int _cursorIndexOfUserFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "userFeedback");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUrl");
          final int _cursorIndexOfProductUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "productUrl");
          final int _cursorIndexOfMerchant = CursorUtil.getColumnIndexOrThrow(_cursor, "merchant");
          final int _cursorIndexOfEstimatedDeliveryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedDeliveryTime");
          final int _cursorIndexOfSustainabilityScore = CursorUtil.getColumnIndexOrThrow(_cursor, "sustainabilityScore");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfTimeInvestment = CursorUtil.getColumnIndexOrThrow(_cursor, "timeInvestment");
          final int _cursorIndexOfRequiredSkills = CursorUtil.getColumnIndexOrThrow(_cursor, "requiredSkills");
          final int _cursorIndexOfSuccessRate = CursorUtil.getColumnIndexOrThrow(_cursor, "successRate");
          final int _cursorIndexOfRelatedAlternatives = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedAlternatives");
          final List<AlternativeProduct> _result = new ArrayList<AlternativeProduct>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AlternativeProduct _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpOriginalProductName;
            if (_cursor.isNull(_cursorIndexOfOriginalProductName)) {
              _tmpOriginalProductName = null;
            } else {
              _tmpOriginalProductName = _cursor.getString(_cursorIndexOfOriginalProductName);
            }
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final String _tmpOriginalCategory;
            if (_cursor.isNull(_cursorIndexOfOriginalCategory)) {
              _tmpOriginalCategory = null;
            } else {
              _tmpOriginalCategory = _cursor.getString(_cursorIndexOfOriginalCategory);
            }
            final String _tmpAlternativeName;
            if (_cursor.isNull(_cursorIndexOfAlternativeName)) {
              _tmpAlternativeName = null;
            } else {
              _tmpAlternativeName = _cursor.getString(_cursorIndexOfAlternativeName);
            }
            final double _tmpAlternativePrice;
            _tmpAlternativePrice = _cursor.getDouble(_cursorIndexOfAlternativePrice);
            final String _tmpAlternativeCategory;
            if (_cursor.isNull(_cursorIndexOfAlternativeCategory)) {
              _tmpAlternativeCategory = null;
            } else {
              _tmpAlternativeCategory = _cursor.getString(_cursorIndexOfAlternativeCategory);
            }
            final String _tmpAlternativeType;
            if (_cursor.isNull(_cursorIndexOfAlternativeType)) {
              _tmpAlternativeType = null;
            } else {
              _tmpAlternativeType = _cursor.getString(_cursorIndexOfAlternativeType);
            }
            final double _tmpSavingsAmount;
            _tmpSavingsAmount = _cursor.getDouble(_cursorIndexOfSavingsAmount);
            final double _tmpSavingsPercentage;
            _tmpSavingsPercentage = _cursor.getDouble(_cursorIndexOfSavingsPercentage);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpPros;
            if (_cursor.isNull(_cursorIndexOfPros)) {
              _tmpPros = null;
            } else {
              _tmpPros = _cursor.getString(_cursorIndexOfPros);
            }
            final String _tmpCons;
            if (_cursor.isNull(_cursorIndexOfCons)) {
              _tmpCons = null;
            } else {
              _tmpCons = _cursor.getString(_cursorIndexOfCons);
            }
            final String _tmpAvailabilityInfo;
            if (_cursor.isNull(_cursorIndexOfAvailabilityInfo)) {
              _tmpAvailabilityInfo = null;
            } else {
              _tmpAvailabilityInfo = _cursor.getString(_cursorIndexOfAvailabilityInfo);
            }
            final Integer _tmpQualityRating;
            if (_cursor.isNull(_cursorIndexOfQualityRating)) {
              _tmpQualityRating = null;
            } else {
              _tmpQualityRating = _cursor.getInt(_cursorIndexOfQualityRating);
            }
            final Integer _tmpUserRating;
            if (_cursor.isNull(_cursorIndexOfUserRating)) {
              _tmpUserRating = null;
            } else {
              _tmpUserRating = _cursor.getInt(_cursorIndexOfUserRating);
            }
            final String _tmpSuggestionSource;
            if (_cursor.isNull(_cursorIndexOfSuggestionSource)) {
              _tmpSuggestionSource = null;
            } else {
              _tmpSuggestionSource = _cursor.getString(_cursorIndexOfSuggestionSource);
            }
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final LocalDateTime _tmpCreatedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCreatedDate);
            }
            _tmpCreatedDate = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpLastSuggested;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfLastSuggested)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfLastSuggested);
            }
            _tmpLastSuggested = __converters.toLocalDateTime(_tmp_1);
            final int _tmpTimesShown;
            _tmpTimesShown = _cursor.getInt(_cursorIndexOfTimesShown);
            final int _tmpTimesAccepted;
            _tmpTimesAccepted = _cursor.getInt(_cursorIndexOfTimesAccepted);
            final int _tmpTimesRejected;
            _tmpTimesRejected = _cursor.getInt(_cursorIndexOfTimesRejected);
            final String _tmpUserFeedback;
            if (_cursor.isNull(_cursorIndexOfUserFeedback)) {
              _tmpUserFeedback = null;
            } else {
              _tmpUserFeedback = _cursor.getString(_cursorIndexOfUserFeedback);
            }
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final String _tmpImageUrl;
            if (_cursor.isNull(_cursorIndexOfImageUrl)) {
              _tmpImageUrl = null;
            } else {
              _tmpImageUrl = _cursor.getString(_cursorIndexOfImageUrl);
            }
            final String _tmpProductUrl;
            if (_cursor.isNull(_cursorIndexOfProductUrl)) {
              _tmpProductUrl = null;
            } else {
              _tmpProductUrl = _cursor.getString(_cursorIndexOfProductUrl);
            }
            final String _tmpMerchant;
            if (_cursor.isNull(_cursorIndexOfMerchant)) {
              _tmpMerchant = null;
            } else {
              _tmpMerchant = _cursor.getString(_cursorIndexOfMerchant);
            }
            final String _tmpEstimatedDeliveryTime;
            if (_cursor.isNull(_cursorIndexOfEstimatedDeliveryTime)) {
              _tmpEstimatedDeliveryTime = null;
            } else {
              _tmpEstimatedDeliveryTime = _cursor.getString(_cursorIndexOfEstimatedDeliveryTime);
            }
            final Integer _tmpSustainabilityScore;
            if (_cursor.isNull(_cursorIndexOfSustainabilityScore)) {
              _tmpSustainabilityScore = null;
            } else {
              _tmpSustainabilityScore = _cursor.getInt(_cursorIndexOfSustainabilityScore);
            }
            final String _tmpDifficultyLevel;
            if (_cursor.isNull(_cursorIndexOfDifficultyLevel)) {
              _tmpDifficultyLevel = null;
            } else {
              _tmpDifficultyLevel = _cursor.getString(_cursorIndexOfDifficultyLevel);
            }
            final String _tmpTimeInvestment;
            if (_cursor.isNull(_cursorIndexOfTimeInvestment)) {
              _tmpTimeInvestment = null;
            } else {
              _tmpTimeInvestment = _cursor.getString(_cursorIndexOfTimeInvestment);
            }
            final String _tmpRequiredSkills;
            if (_cursor.isNull(_cursorIndexOfRequiredSkills)) {
              _tmpRequiredSkills = null;
            } else {
              _tmpRequiredSkills = _cursor.getString(_cursorIndexOfRequiredSkills);
            }
            final Double _tmpSuccessRate;
            if (_cursor.isNull(_cursorIndexOfSuccessRate)) {
              _tmpSuccessRate = null;
            } else {
              _tmpSuccessRate = _cursor.getDouble(_cursorIndexOfSuccessRate);
            }
            final String _tmpRelatedAlternatives;
            if (_cursor.isNull(_cursorIndexOfRelatedAlternatives)) {
              _tmpRelatedAlternatives = null;
            } else {
              _tmpRelatedAlternatives = _cursor.getString(_cursorIndexOfRelatedAlternatives);
            }
            _item = new AlternativeProduct(_tmpId,_tmpOriginalProductName,_tmpOriginalPrice,_tmpOriginalCategory,_tmpAlternativeName,_tmpAlternativePrice,_tmpAlternativeCategory,_tmpAlternativeType,_tmpSavingsAmount,_tmpSavingsPercentage,_tmpDescription,_tmpPros,_tmpCons,_tmpAvailabilityInfo,_tmpQualityRating,_tmpUserRating,_tmpSuggestionSource,_tmpConfidenceScore,_tmpCreatedDate,_tmpLastSuggested,_tmpTimesShown,_tmpTimesAccepted,_tmpTimesRejected,_tmpUserFeedback,_tmpIsActive,_tmpTags,_tmpImageUrl,_tmpProductUrl,_tmpMerchant,_tmpEstimatedDeliveryTime,_tmpSustainabilityScore,_tmpDifficultyLevel,_tmpTimeInvestment,_tmpRequiredSkills,_tmpSuccessRate,_tmpRelatedAlternatives);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<AlternativeProduct>> getAlternativesByType(final String type) {
    final String _sql = "SELECT * FROM alternative_products WHERE alternativeType = ? AND isActive = 1 ORDER BY savingsAmount DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (type == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, type);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"alternative_products"}, new Callable<List<AlternativeProduct>>() {
      @Override
      @NonNull
      public List<AlternativeProduct> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfOriginalProductName = CursorUtil.getColumnIndexOrThrow(_cursor, "originalProductName");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfOriginalCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "originalCategory");
          final int _cursorIndexOfAlternativeName = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeName");
          final int _cursorIndexOfAlternativePrice = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativePrice");
          final int _cursorIndexOfAlternativeCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeCategory");
          final int _cursorIndexOfAlternativeType = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeType");
          final int _cursorIndexOfSavingsAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "savingsAmount");
          final int _cursorIndexOfSavingsPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "savingsPercentage");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfPros = CursorUtil.getColumnIndexOrThrow(_cursor, "pros");
          final int _cursorIndexOfCons = CursorUtil.getColumnIndexOrThrow(_cursor, "cons");
          final int _cursorIndexOfAvailabilityInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "availabilityInfo");
          final int _cursorIndexOfQualityRating = CursorUtil.getColumnIndexOrThrow(_cursor, "qualityRating");
          final int _cursorIndexOfUserRating = CursorUtil.getColumnIndexOrThrow(_cursor, "userRating");
          final int _cursorIndexOfSuggestionSource = CursorUtil.getColumnIndexOrThrow(_cursor, "suggestionSource");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfCreatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "createdDate");
          final int _cursorIndexOfLastSuggested = CursorUtil.getColumnIndexOrThrow(_cursor, "lastSuggested");
          final int _cursorIndexOfTimesShown = CursorUtil.getColumnIndexOrThrow(_cursor, "timesShown");
          final int _cursorIndexOfTimesAccepted = CursorUtil.getColumnIndexOrThrow(_cursor, "timesAccepted");
          final int _cursorIndexOfTimesRejected = CursorUtil.getColumnIndexOrThrow(_cursor, "timesRejected");
          final int _cursorIndexOfUserFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "userFeedback");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUrl");
          final int _cursorIndexOfProductUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "productUrl");
          final int _cursorIndexOfMerchant = CursorUtil.getColumnIndexOrThrow(_cursor, "merchant");
          final int _cursorIndexOfEstimatedDeliveryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedDeliveryTime");
          final int _cursorIndexOfSustainabilityScore = CursorUtil.getColumnIndexOrThrow(_cursor, "sustainabilityScore");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfTimeInvestment = CursorUtil.getColumnIndexOrThrow(_cursor, "timeInvestment");
          final int _cursorIndexOfRequiredSkills = CursorUtil.getColumnIndexOrThrow(_cursor, "requiredSkills");
          final int _cursorIndexOfSuccessRate = CursorUtil.getColumnIndexOrThrow(_cursor, "successRate");
          final int _cursorIndexOfRelatedAlternatives = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedAlternatives");
          final List<AlternativeProduct> _result = new ArrayList<AlternativeProduct>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AlternativeProduct _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpOriginalProductName;
            if (_cursor.isNull(_cursorIndexOfOriginalProductName)) {
              _tmpOriginalProductName = null;
            } else {
              _tmpOriginalProductName = _cursor.getString(_cursorIndexOfOriginalProductName);
            }
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final String _tmpOriginalCategory;
            if (_cursor.isNull(_cursorIndexOfOriginalCategory)) {
              _tmpOriginalCategory = null;
            } else {
              _tmpOriginalCategory = _cursor.getString(_cursorIndexOfOriginalCategory);
            }
            final String _tmpAlternativeName;
            if (_cursor.isNull(_cursorIndexOfAlternativeName)) {
              _tmpAlternativeName = null;
            } else {
              _tmpAlternativeName = _cursor.getString(_cursorIndexOfAlternativeName);
            }
            final double _tmpAlternativePrice;
            _tmpAlternativePrice = _cursor.getDouble(_cursorIndexOfAlternativePrice);
            final String _tmpAlternativeCategory;
            if (_cursor.isNull(_cursorIndexOfAlternativeCategory)) {
              _tmpAlternativeCategory = null;
            } else {
              _tmpAlternativeCategory = _cursor.getString(_cursorIndexOfAlternativeCategory);
            }
            final String _tmpAlternativeType;
            if (_cursor.isNull(_cursorIndexOfAlternativeType)) {
              _tmpAlternativeType = null;
            } else {
              _tmpAlternativeType = _cursor.getString(_cursorIndexOfAlternativeType);
            }
            final double _tmpSavingsAmount;
            _tmpSavingsAmount = _cursor.getDouble(_cursorIndexOfSavingsAmount);
            final double _tmpSavingsPercentage;
            _tmpSavingsPercentage = _cursor.getDouble(_cursorIndexOfSavingsPercentage);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpPros;
            if (_cursor.isNull(_cursorIndexOfPros)) {
              _tmpPros = null;
            } else {
              _tmpPros = _cursor.getString(_cursorIndexOfPros);
            }
            final String _tmpCons;
            if (_cursor.isNull(_cursorIndexOfCons)) {
              _tmpCons = null;
            } else {
              _tmpCons = _cursor.getString(_cursorIndexOfCons);
            }
            final String _tmpAvailabilityInfo;
            if (_cursor.isNull(_cursorIndexOfAvailabilityInfo)) {
              _tmpAvailabilityInfo = null;
            } else {
              _tmpAvailabilityInfo = _cursor.getString(_cursorIndexOfAvailabilityInfo);
            }
            final Integer _tmpQualityRating;
            if (_cursor.isNull(_cursorIndexOfQualityRating)) {
              _tmpQualityRating = null;
            } else {
              _tmpQualityRating = _cursor.getInt(_cursorIndexOfQualityRating);
            }
            final Integer _tmpUserRating;
            if (_cursor.isNull(_cursorIndexOfUserRating)) {
              _tmpUserRating = null;
            } else {
              _tmpUserRating = _cursor.getInt(_cursorIndexOfUserRating);
            }
            final String _tmpSuggestionSource;
            if (_cursor.isNull(_cursorIndexOfSuggestionSource)) {
              _tmpSuggestionSource = null;
            } else {
              _tmpSuggestionSource = _cursor.getString(_cursorIndexOfSuggestionSource);
            }
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final LocalDateTime _tmpCreatedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCreatedDate);
            }
            _tmpCreatedDate = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpLastSuggested;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfLastSuggested)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfLastSuggested);
            }
            _tmpLastSuggested = __converters.toLocalDateTime(_tmp_1);
            final int _tmpTimesShown;
            _tmpTimesShown = _cursor.getInt(_cursorIndexOfTimesShown);
            final int _tmpTimesAccepted;
            _tmpTimesAccepted = _cursor.getInt(_cursorIndexOfTimesAccepted);
            final int _tmpTimesRejected;
            _tmpTimesRejected = _cursor.getInt(_cursorIndexOfTimesRejected);
            final String _tmpUserFeedback;
            if (_cursor.isNull(_cursorIndexOfUserFeedback)) {
              _tmpUserFeedback = null;
            } else {
              _tmpUserFeedback = _cursor.getString(_cursorIndexOfUserFeedback);
            }
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final String _tmpImageUrl;
            if (_cursor.isNull(_cursorIndexOfImageUrl)) {
              _tmpImageUrl = null;
            } else {
              _tmpImageUrl = _cursor.getString(_cursorIndexOfImageUrl);
            }
            final String _tmpProductUrl;
            if (_cursor.isNull(_cursorIndexOfProductUrl)) {
              _tmpProductUrl = null;
            } else {
              _tmpProductUrl = _cursor.getString(_cursorIndexOfProductUrl);
            }
            final String _tmpMerchant;
            if (_cursor.isNull(_cursorIndexOfMerchant)) {
              _tmpMerchant = null;
            } else {
              _tmpMerchant = _cursor.getString(_cursorIndexOfMerchant);
            }
            final String _tmpEstimatedDeliveryTime;
            if (_cursor.isNull(_cursorIndexOfEstimatedDeliveryTime)) {
              _tmpEstimatedDeliveryTime = null;
            } else {
              _tmpEstimatedDeliveryTime = _cursor.getString(_cursorIndexOfEstimatedDeliveryTime);
            }
            final Integer _tmpSustainabilityScore;
            if (_cursor.isNull(_cursorIndexOfSustainabilityScore)) {
              _tmpSustainabilityScore = null;
            } else {
              _tmpSustainabilityScore = _cursor.getInt(_cursorIndexOfSustainabilityScore);
            }
            final String _tmpDifficultyLevel;
            if (_cursor.isNull(_cursorIndexOfDifficultyLevel)) {
              _tmpDifficultyLevel = null;
            } else {
              _tmpDifficultyLevel = _cursor.getString(_cursorIndexOfDifficultyLevel);
            }
            final String _tmpTimeInvestment;
            if (_cursor.isNull(_cursorIndexOfTimeInvestment)) {
              _tmpTimeInvestment = null;
            } else {
              _tmpTimeInvestment = _cursor.getString(_cursorIndexOfTimeInvestment);
            }
            final String _tmpRequiredSkills;
            if (_cursor.isNull(_cursorIndexOfRequiredSkills)) {
              _tmpRequiredSkills = null;
            } else {
              _tmpRequiredSkills = _cursor.getString(_cursorIndexOfRequiredSkills);
            }
            final Double _tmpSuccessRate;
            if (_cursor.isNull(_cursorIndexOfSuccessRate)) {
              _tmpSuccessRate = null;
            } else {
              _tmpSuccessRate = _cursor.getDouble(_cursorIndexOfSuccessRate);
            }
            final String _tmpRelatedAlternatives;
            if (_cursor.isNull(_cursorIndexOfRelatedAlternatives)) {
              _tmpRelatedAlternatives = null;
            } else {
              _tmpRelatedAlternatives = _cursor.getString(_cursorIndexOfRelatedAlternatives);
            }
            _item = new AlternativeProduct(_tmpId,_tmpOriginalProductName,_tmpOriginalPrice,_tmpOriginalCategory,_tmpAlternativeName,_tmpAlternativePrice,_tmpAlternativeCategory,_tmpAlternativeType,_tmpSavingsAmount,_tmpSavingsPercentage,_tmpDescription,_tmpPros,_tmpCons,_tmpAvailabilityInfo,_tmpQualityRating,_tmpUserRating,_tmpSuggestionSource,_tmpConfidenceScore,_tmpCreatedDate,_tmpLastSuggested,_tmpTimesShown,_tmpTimesAccepted,_tmpTimesRejected,_tmpUserFeedback,_tmpIsActive,_tmpTags,_tmpImageUrl,_tmpProductUrl,_tmpMerchant,_tmpEstimatedDeliveryTime,_tmpSustainabilityScore,_tmpDifficultyLevel,_tmpTimeInvestment,_tmpRequiredSkills,_tmpSuccessRate,_tmpRelatedAlternatives);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<AlternativeProduct>> getAlternativesForProduct(final String productName) {
    final String _sql = "SELECT * FROM alternative_products WHERE originalProductName LIKE '%' || ? || '%' AND isActive = 1 ORDER BY confidenceScore DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (productName == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, productName);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"alternative_products"}, new Callable<List<AlternativeProduct>>() {
      @Override
      @NonNull
      public List<AlternativeProduct> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfOriginalProductName = CursorUtil.getColumnIndexOrThrow(_cursor, "originalProductName");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfOriginalCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "originalCategory");
          final int _cursorIndexOfAlternativeName = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeName");
          final int _cursorIndexOfAlternativePrice = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativePrice");
          final int _cursorIndexOfAlternativeCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeCategory");
          final int _cursorIndexOfAlternativeType = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeType");
          final int _cursorIndexOfSavingsAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "savingsAmount");
          final int _cursorIndexOfSavingsPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "savingsPercentage");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfPros = CursorUtil.getColumnIndexOrThrow(_cursor, "pros");
          final int _cursorIndexOfCons = CursorUtil.getColumnIndexOrThrow(_cursor, "cons");
          final int _cursorIndexOfAvailabilityInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "availabilityInfo");
          final int _cursorIndexOfQualityRating = CursorUtil.getColumnIndexOrThrow(_cursor, "qualityRating");
          final int _cursorIndexOfUserRating = CursorUtil.getColumnIndexOrThrow(_cursor, "userRating");
          final int _cursorIndexOfSuggestionSource = CursorUtil.getColumnIndexOrThrow(_cursor, "suggestionSource");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfCreatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "createdDate");
          final int _cursorIndexOfLastSuggested = CursorUtil.getColumnIndexOrThrow(_cursor, "lastSuggested");
          final int _cursorIndexOfTimesShown = CursorUtil.getColumnIndexOrThrow(_cursor, "timesShown");
          final int _cursorIndexOfTimesAccepted = CursorUtil.getColumnIndexOrThrow(_cursor, "timesAccepted");
          final int _cursorIndexOfTimesRejected = CursorUtil.getColumnIndexOrThrow(_cursor, "timesRejected");
          final int _cursorIndexOfUserFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "userFeedback");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUrl");
          final int _cursorIndexOfProductUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "productUrl");
          final int _cursorIndexOfMerchant = CursorUtil.getColumnIndexOrThrow(_cursor, "merchant");
          final int _cursorIndexOfEstimatedDeliveryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedDeliveryTime");
          final int _cursorIndexOfSustainabilityScore = CursorUtil.getColumnIndexOrThrow(_cursor, "sustainabilityScore");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfTimeInvestment = CursorUtil.getColumnIndexOrThrow(_cursor, "timeInvestment");
          final int _cursorIndexOfRequiredSkills = CursorUtil.getColumnIndexOrThrow(_cursor, "requiredSkills");
          final int _cursorIndexOfSuccessRate = CursorUtil.getColumnIndexOrThrow(_cursor, "successRate");
          final int _cursorIndexOfRelatedAlternatives = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedAlternatives");
          final List<AlternativeProduct> _result = new ArrayList<AlternativeProduct>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AlternativeProduct _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpOriginalProductName;
            if (_cursor.isNull(_cursorIndexOfOriginalProductName)) {
              _tmpOriginalProductName = null;
            } else {
              _tmpOriginalProductName = _cursor.getString(_cursorIndexOfOriginalProductName);
            }
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final String _tmpOriginalCategory;
            if (_cursor.isNull(_cursorIndexOfOriginalCategory)) {
              _tmpOriginalCategory = null;
            } else {
              _tmpOriginalCategory = _cursor.getString(_cursorIndexOfOriginalCategory);
            }
            final String _tmpAlternativeName;
            if (_cursor.isNull(_cursorIndexOfAlternativeName)) {
              _tmpAlternativeName = null;
            } else {
              _tmpAlternativeName = _cursor.getString(_cursorIndexOfAlternativeName);
            }
            final double _tmpAlternativePrice;
            _tmpAlternativePrice = _cursor.getDouble(_cursorIndexOfAlternativePrice);
            final String _tmpAlternativeCategory;
            if (_cursor.isNull(_cursorIndexOfAlternativeCategory)) {
              _tmpAlternativeCategory = null;
            } else {
              _tmpAlternativeCategory = _cursor.getString(_cursorIndexOfAlternativeCategory);
            }
            final String _tmpAlternativeType;
            if (_cursor.isNull(_cursorIndexOfAlternativeType)) {
              _tmpAlternativeType = null;
            } else {
              _tmpAlternativeType = _cursor.getString(_cursorIndexOfAlternativeType);
            }
            final double _tmpSavingsAmount;
            _tmpSavingsAmount = _cursor.getDouble(_cursorIndexOfSavingsAmount);
            final double _tmpSavingsPercentage;
            _tmpSavingsPercentage = _cursor.getDouble(_cursorIndexOfSavingsPercentage);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpPros;
            if (_cursor.isNull(_cursorIndexOfPros)) {
              _tmpPros = null;
            } else {
              _tmpPros = _cursor.getString(_cursorIndexOfPros);
            }
            final String _tmpCons;
            if (_cursor.isNull(_cursorIndexOfCons)) {
              _tmpCons = null;
            } else {
              _tmpCons = _cursor.getString(_cursorIndexOfCons);
            }
            final String _tmpAvailabilityInfo;
            if (_cursor.isNull(_cursorIndexOfAvailabilityInfo)) {
              _tmpAvailabilityInfo = null;
            } else {
              _tmpAvailabilityInfo = _cursor.getString(_cursorIndexOfAvailabilityInfo);
            }
            final Integer _tmpQualityRating;
            if (_cursor.isNull(_cursorIndexOfQualityRating)) {
              _tmpQualityRating = null;
            } else {
              _tmpQualityRating = _cursor.getInt(_cursorIndexOfQualityRating);
            }
            final Integer _tmpUserRating;
            if (_cursor.isNull(_cursorIndexOfUserRating)) {
              _tmpUserRating = null;
            } else {
              _tmpUserRating = _cursor.getInt(_cursorIndexOfUserRating);
            }
            final String _tmpSuggestionSource;
            if (_cursor.isNull(_cursorIndexOfSuggestionSource)) {
              _tmpSuggestionSource = null;
            } else {
              _tmpSuggestionSource = _cursor.getString(_cursorIndexOfSuggestionSource);
            }
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final LocalDateTime _tmpCreatedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCreatedDate);
            }
            _tmpCreatedDate = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpLastSuggested;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfLastSuggested)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfLastSuggested);
            }
            _tmpLastSuggested = __converters.toLocalDateTime(_tmp_1);
            final int _tmpTimesShown;
            _tmpTimesShown = _cursor.getInt(_cursorIndexOfTimesShown);
            final int _tmpTimesAccepted;
            _tmpTimesAccepted = _cursor.getInt(_cursorIndexOfTimesAccepted);
            final int _tmpTimesRejected;
            _tmpTimesRejected = _cursor.getInt(_cursorIndexOfTimesRejected);
            final String _tmpUserFeedback;
            if (_cursor.isNull(_cursorIndexOfUserFeedback)) {
              _tmpUserFeedback = null;
            } else {
              _tmpUserFeedback = _cursor.getString(_cursorIndexOfUserFeedback);
            }
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final String _tmpImageUrl;
            if (_cursor.isNull(_cursorIndexOfImageUrl)) {
              _tmpImageUrl = null;
            } else {
              _tmpImageUrl = _cursor.getString(_cursorIndexOfImageUrl);
            }
            final String _tmpProductUrl;
            if (_cursor.isNull(_cursorIndexOfProductUrl)) {
              _tmpProductUrl = null;
            } else {
              _tmpProductUrl = _cursor.getString(_cursorIndexOfProductUrl);
            }
            final String _tmpMerchant;
            if (_cursor.isNull(_cursorIndexOfMerchant)) {
              _tmpMerchant = null;
            } else {
              _tmpMerchant = _cursor.getString(_cursorIndexOfMerchant);
            }
            final String _tmpEstimatedDeliveryTime;
            if (_cursor.isNull(_cursorIndexOfEstimatedDeliveryTime)) {
              _tmpEstimatedDeliveryTime = null;
            } else {
              _tmpEstimatedDeliveryTime = _cursor.getString(_cursorIndexOfEstimatedDeliveryTime);
            }
            final Integer _tmpSustainabilityScore;
            if (_cursor.isNull(_cursorIndexOfSustainabilityScore)) {
              _tmpSustainabilityScore = null;
            } else {
              _tmpSustainabilityScore = _cursor.getInt(_cursorIndexOfSustainabilityScore);
            }
            final String _tmpDifficultyLevel;
            if (_cursor.isNull(_cursorIndexOfDifficultyLevel)) {
              _tmpDifficultyLevel = null;
            } else {
              _tmpDifficultyLevel = _cursor.getString(_cursorIndexOfDifficultyLevel);
            }
            final String _tmpTimeInvestment;
            if (_cursor.isNull(_cursorIndexOfTimeInvestment)) {
              _tmpTimeInvestment = null;
            } else {
              _tmpTimeInvestment = _cursor.getString(_cursorIndexOfTimeInvestment);
            }
            final String _tmpRequiredSkills;
            if (_cursor.isNull(_cursorIndexOfRequiredSkills)) {
              _tmpRequiredSkills = null;
            } else {
              _tmpRequiredSkills = _cursor.getString(_cursorIndexOfRequiredSkills);
            }
            final Double _tmpSuccessRate;
            if (_cursor.isNull(_cursorIndexOfSuccessRate)) {
              _tmpSuccessRate = null;
            } else {
              _tmpSuccessRate = _cursor.getDouble(_cursorIndexOfSuccessRate);
            }
            final String _tmpRelatedAlternatives;
            if (_cursor.isNull(_cursorIndexOfRelatedAlternatives)) {
              _tmpRelatedAlternatives = null;
            } else {
              _tmpRelatedAlternatives = _cursor.getString(_cursorIndexOfRelatedAlternatives);
            }
            _item = new AlternativeProduct(_tmpId,_tmpOriginalProductName,_tmpOriginalPrice,_tmpOriginalCategory,_tmpAlternativeName,_tmpAlternativePrice,_tmpAlternativeCategory,_tmpAlternativeType,_tmpSavingsAmount,_tmpSavingsPercentage,_tmpDescription,_tmpPros,_tmpCons,_tmpAvailabilityInfo,_tmpQualityRating,_tmpUserRating,_tmpSuggestionSource,_tmpConfidenceScore,_tmpCreatedDate,_tmpLastSuggested,_tmpTimesShown,_tmpTimesAccepted,_tmpTimesRejected,_tmpUserFeedback,_tmpIsActive,_tmpTags,_tmpImageUrl,_tmpProductUrl,_tmpMerchant,_tmpEstimatedDeliveryTime,_tmpSustainabilityScore,_tmpDifficultyLevel,_tmpTimeInvestment,_tmpRequiredSkills,_tmpSuccessRate,_tmpRelatedAlternatives);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<AlternativeProduct>> getAlternativesBySavings(final double minSavings) {
    final String _sql = "SELECT * FROM alternative_products WHERE savingsAmount >= ? AND isActive = 1 ORDER BY savingsAmount DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindDouble(_argIndex, minSavings);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"alternative_products"}, new Callable<List<AlternativeProduct>>() {
      @Override
      @NonNull
      public List<AlternativeProduct> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfOriginalProductName = CursorUtil.getColumnIndexOrThrow(_cursor, "originalProductName");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfOriginalCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "originalCategory");
          final int _cursorIndexOfAlternativeName = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeName");
          final int _cursorIndexOfAlternativePrice = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativePrice");
          final int _cursorIndexOfAlternativeCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeCategory");
          final int _cursorIndexOfAlternativeType = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeType");
          final int _cursorIndexOfSavingsAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "savingsAmount");
          final int _cursorIndexOfSavingsPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "savingsPercentage");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfPros = CursorUtil.getColumnIndexOrThrow(_cursor, "pros");
          final int _cursorIndexOfCons = CursorUtil.getColumnIndexOrThrow(_cursor, "cons");
          final int _cursorIndexOfAvailabilityInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "availabilityInfo");
          final int _cursorIndexOfQualityRating = CursorUtil.getColumnIndexOrThrow(_cursor, "qualityRating");
          final int _cursorIndexOfUserRating = CursorUtil.getColumnIndexOrThrow(_cursor, "userRating");
          final int _cursorIndexOfSuggestionSource = CursorUtil.getColumnIndexOrThrow(_cursor, "suggestionSource");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfCreatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "createdDate");
          final int _cursorIndexOfLastSuggested = CursorUtil.getColumnIndexOrThrow(_cursor, "lastSuggested");
          final int _cursorIndexOfTimesShown = CursorUtil.getColumnIndexOrThrow(_cursor, "timesShown");
          final int _cursorIndexOfTimesAccepted = CursorUtil.getColumnIndexOrThrow(_cursor, "timesAccepted");
          final int _cursorIndexOfTimesRejected = CursorUtil.getColumnIndexOrThrow(_cursor, "timesRejected");
          final int _cursorIndexOfUserFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "userFeedback");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUrl");
          final int _cursorIndexOfProductUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "productUrl");
          final int _cursorIndexOfMerchant = CursorUtil.getColumnIndexOrThrow(_cursor, "merchant");
          final int _cursorIndexOfEstimatedDeliveryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedDeliveryTime");
          final int _cursorIndexOfSustainabilityScore = CursorUtil.getColumnIndexOrThrow(_cursor, "sustainabilityScore");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfTimeInvestment = CursorUtil.getColumnIndexOrThrow(_cursor, "timeInvestment");
          final int _cursorIndexOfRequiredSkills = CursorUtil.getColumnIndexOrThrow(_cursor, "requiredSkills");
          final int _cursorIndexOfSuccessRate = CursorUtil.getColumnIndexOrThrow(_cursor, "successRate");
          final int _cursorIndexOfRelatedAlternatives = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedAlternatives");
          final List<AlternativeProduct> _result = new ArrayList<AlternativeProduct>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AlternativeProduct _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpOriginalProductName;
            if (_cursor.isNull(_cursorIndexOfOriginalProductName)) {
              _tmpOriginalProductName = null;
            } else {
              _tmpOriginalProductName = _cursor.getString(_cursorIndexOfOriginalProductName);
            }
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final String _tmpOriginalCategory;
            if (_cursor.isNull(_cursorIndexOfOriginalCategory)) {
              _tmpOriginalCategory = null;
            } else {
              _tmpOriginalCategory = _cursor.getString(_cursorIndexOfOriginalCategory);
            }
            final String _tmpAlternativeName;
            if (_cursor.isNull(_cursorIndexOfAlternativeName)) {
              _tmpAlternativeName = null;
            } else {
              _tmpAlternativeName = _cursor.getString(_cursorIndexOfAlternativeName);
            }
            final double _tmpAlternativePrice;
            _tmpAlternativePrice = _cursor.getDouble(_cursorIndexOfAlternativePrice);
            final String _tmpAlternativeCategory;
            if (_cursor.isNull(_cursorIndexOfAlternativeCategory)) {
              _tmpAlternativeCategory = null;
            } else {
              _tmpAlternativeCategory = _cursor.getString(_cursorIndexOfAlternativeCategory);
            }
            final String _tmpAlternativeType;
            if (_cursor.isNull(_cursorIndexOfAlternativeType)) {
              _tmpAlternativeType = null;
            } else {
              _tmpAlternativeType = _cursor.getString(_cursorIndexOfAlternativeType);
            }
            final double _tmpSavingsAmount;
            _tmpSavingsAmount = _cursor.getDouble(_cursorIndexOfSavingsAmount);
            final double _tmpSavingsPercentage;
            _tmpSavingsPercentage = _cursor.getDouble(_cursorIndexOfSavingsPercentage);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpPros;
            if (_cursor.isNull(_cursorIndexOfPros)) {
              _tmpPros = null;
            } else {
              _tmpPros = _cursor.getString(_cursorIndexOfPros);
            }
            final String _tmpCons;
            if (_cursor.isNull(_cursorIndexOfCons)) {
              _tmpCons = null;
            } else {
              _tmpCons = _cursor.getString(_cursorIndexOfCons);
            }
            final String _tmpAvailabilityInfo;
            if (_cursor.isNull(_cursorIndexOfAvailabilityInfo)) {
              _tmpAvailabilityInfo = null;
            } else {
              _tmpAvailabilityInfo = _cursor.getString(_cursorIndexOfAvailabilityInfo);
            }
            final Integer _tmpQualityRating;
            if (_cursor.isNull(_cursorIndexOfQualityRating)) {
              _tmpQualityRating = null;
            } else {
              _tmpQualityRating = _cursor.getInt(_cursorIndexOfQualityRating);
            }
            final Integer _tmpUserRating;
            if (_cursor.isNull(_cursorIndexOfUserRating)) {
              _tmpUserRating = null;
            } else {
              _tmpUserRating = _cursor.getInt(_cursorIndexOfUserRating);
            }
            final String _tmpSuggestionSource;
            if (_cursor.isNull(_cursorIndexOfSuggestionSource)) {
              _tmpSuggestionSource = null;
            } else {
              _tmpSuggestionSource = _cursor.getString(_cursorIndexOfSuggestionSource);
            }
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final LocalDateTime _tmpCreatedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCreatedDate);
            }
            _tmpCreatedDate = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpLastSuggested;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfLastSuggested)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfLastSuggested);
            }
            _tmpLastSuggested = __converters.toLocalDateTime(_tmp_1);
            final int _tmpTimesShown;
            _tmpTimesShown = _cursor.getInt(_cursorIndexOfTimesShown);
            final int _tmpTimesAccepted;
            _tmpTimesAccepted = _cursor.getInt(_cursorIndexOfTimesAccepted);
            final int _tmpTimesRejected;
            _tmpTimesRejected = _cursor.getInt(_cursorIndexOfTimesRejected);
            final String _tmpUserFeedback;
            if (_cursor.isNull(_cursorIndexOfUserFeedback)) {
              _tmpUserFeedback = null;
            } else {
              _tmpUserFeedback = _cursor.getString(_cursorIndexOfUserFeedback);
            }
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final String _tmpImageUrl;
            if (_cursor.isNull(_cursorIndexOfImageUrl)) {
              _tmpImageUrl = null;
            } else {
              _tmpImageUrl = _cursor.getString(_cursorIndexOfImageUrl);
            }
            final String _tmpProductUrl;
            if (_cursor.isNull(_cursorIndexOfProductUrl)) {
              _tmpProductUrl = null;
            } else {
              _tmpProductUrl = _cursor.getString(_cursorIndexOfProductUrl);
            }
            final String _tmpMerchant;
            if (_cursor.isNull(_cursorIndexOfMerchant)) {
              _tmpMerchant = null;
            } else {
              _tmpMerchant = _cursor.getString(_cursorIndexOfMerchant);
            }
            final String _tmpEstimatedDeliveryTime;
            if (_cursor.isNull(_cursorIndexOfEstimatedDeliveryTime)) {
              _tmpEstimatedDeliveryTime = null;
            } else {
              _tmpEstimatedDeliveryTime = _cursor.getString(_cursorIndexOfEstimatedDeliveryTime);
            }
            final Integer _tmpSustainabilityScore;
            if (_cursor.isNull(_cursorIndexOfSustainabilityScore)) {
              _tmpSustainabilityScore = null;
            } else {
              _tmpSustainabilityScore = _cursor.getInt(_cursorIndexOfSustainabilityScore);
            }
            final String _tmpDifficultyLevel;
            if (_cursor.isNull(_cursorIndexOfDifficultyLevel)) {
              _tmpDifficultyLevel = null;
            } else {
              _tmpDifficultyLevel = _cursor.getString(_cursorIndexOfDifficultyLevel);
            }
            final String _tmpTimeInvestment;
            if (_cursor.isNull(_cursorIndexOfTimeInvestment)) {
              _tmpTimeInvestment = null;
            } else {
              _tmpTimeInvestment = _cursor.getString(_cursorIndexOfTimeInvestment);
            }
            final String _tmpRequiredSkills;
            if (_cursor.isNull(_cursorIndexOfRequiredSkills)) {
              _tmpRequiredSkills = null;
            } else {
              _tmpRequiredSkills = _cursor.getString(_cursorIndexOfRequiredSkills);
            }
            final Double _tmpSuccessRate;
            if (_cursor.isNull(_cursorIndexOfSuccessRate)) {
              _tmpSuccessRate = null;
            } else {
              _tmpSuccessRate = _cursor.getDouble(_cursorIndexOfSuccessRate);
            }
            final String _tmpRelatedAlternatives;
            if (_cursor.isNull(_cursorIndexOfRelatedAlternatives)) {
              _tmpRelatedAlternatives = null;
            } else {
              _tmpRelatedAlternatives = _cursor.getString(_cursorIndexOfRelatedAlternatives);
            }
            _item = new AlternativeProduct(_tmpId,_tmpOriginalProductName,_tmpOriginalPrice,_tmpOriginalCategory,_tmpAlternativeName,_tmpAlternativePrice,_tmpAlternativeCategory,_tmpAlternativeType,_tmpSavingsAmount,_tmpSavingsPercentage,_tmpDescription,_tmpPros,_tmpCons,_tmpAvailabilityInfo,_tmpQualityRating,_tmpUserRating,_tmpSuggestionSource,_tmpConfidenceScore,_tmpCreatedDate,_tmpLastSuggested,_tmpTimesShown,_tmpTimesAccepted,_tmpTimesRejected,_tmpUserFeedback,_tmpIsActive,_tmpTags,_tmpImageUrl,_tmpProductUrl,_tmpMerchant,_tmpEstimatedDeliveryTime,_tmpSustainabilityScore,_tmpDifficultyLevel,_tmpTimeInvestment,_tmpRequiredSkills,_tmpSuccessRate,_tmpRelatedAlternatives);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getAlternativeById(final long id,
      final Continuation<? super AlternativeProduct> $completion) {
    final String _sql = "SELECT * FROM alternative_products WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<AlternativeProduct>() {
      @Override
      @Nullable
      public AlternativeProduct call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfOriginalProductName = CursorUtil.getColumnIndexOrThrow(_cursor, "originalProductName");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfOriginalCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "originalCategory");
          final int _cursorIndexOfAlternativeName = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeName");
          final int _cursorIndexOfAlternativePrice = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativePrice");
          final int _cursorIndexOfAlternativeCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeCategory");
          final int _cursorIndexOfAlternativeType = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeType");
          final int _cursorIndexOfSavingsAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "savingsAmount");
          final int _cursorIndexOfSavingsPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "savingsPercentage");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfPros = CursorUtil.getColumnIndexOrThrow(_cursor, "pros");
          final int _cursorIndexOfCons = CursorUtil.getColumnIndexOrThrow(_cursor, "cons");
          final int _cursorIndexOfAvailabilityInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "availabilityInfo");
          final int _cursorIndexOfQualityRating = CursorUtil.getColumnIndexOrThrow(_cursor, "qualityRating");
          final int _cursorIndexOfUserRating = CursorUtil.getColumnIndexOrThrow(_cursor, "userRating");
          final int _cursorIndexOfSuggestionSource = CursorUtil.getColumnIndexOrThrow(_cursor, "suggestionSource");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfCreatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "createdDate");
          final int _cursorIndexOfLastSuggested = CursorUtil.getColumnIndexOrThrow(_cursor, "lastSuggested");
          final int _cursorIndexOfTimesShown = CursorUtil.getColumnIndexOrThrow(_cursor, "timesShown");
          final int _cursorIndexOfTimesAccepted = CursorUtil.getColumnIndexOrThrow(_cursor, "timesAccepted");
          final int _cursorIndexOfTimesRejected = CursorUtil.getColumnIndexOrThrow(_cursor, "timesRejected");
          final int _cursorIndexOfUserFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "userFeedback");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUrl");
          final int _cursorIndexOfProductUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "productUrl");
          final int _cursorIndexOfMerchant = CursorUtil.getColumnIndexOrThrow(_cursor, "merchant");
          final int _cursorIndexOfEstimatedDeliveryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedDeliveryTime");
          final int _cursorIndexOfSustainabilityScore = CursorUtil.getColumnIndexOrThrow(_cursor, "sustainabilityScore");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfTimeInvestment = CursorUtil.getColumnIndexOrThrow(_cursor, "timeInvestment");
          final int _cursorIndexOfRequiredSkills = CursorUtil.getColumnIndexOrThrow(_cursor, "requiredSkills");
          final int _cursorIndexOfSuccessRate = CursorUtil.getColumnIndexOrThrow(_cursor, "successRate");
          final int _cursorIndexOfRelatedAlternatives = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedAlternatives");
          final AlternativeProduct _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpOriginalProductName;
            if (_cursor.isNull(_cursorIndexOfOriginalProductName)) {
              _tmpOriginalProductName = null;
            } else {
              _tmpOriginalProductName = _cursor.getString(_cursorIndexOfOriginalProductName);
            }
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final String _tmpOriginalCategory;
            if (_cursor.isNull(_cursorIndexOfOriginalCategory)) {
              _tmpOriginalCategory = null;
            } else {
              _tmpOriginalCategory = _cursor.getString(_cursorIndexOfOriginalCategory);
            }
            final String _tmpAlternativeName;
            if (_cursor.isNull(_cursorIndexOfAlternativeName)) {
              _tmpAlternativeName = null;
            } else {
              _tmpAlternativeName = _cursor.getString(_cursorIndexOfAlternativeName);
            }
            final double _tmpAlternativePrice;
            _tmpAlternativePrice = _cursor.getDouble(_cursorIndexOfAlternativePrice);
            final String _tmpAlternativeCategory;
            if (_cursor.isNull(_cursorIndexOfAlternativeCategory)) {
              _tmpAlternativeCategory = null;
            } else {
              _tmpAlternativeCategory = _cursor.getString(_cursorIndexOfAlternativeCategory);
            }
            final String _tmpAlternativeType;
            if (_cursor.isNull(_cursorIndexOfAlternativeType)) {
              _tmpAlternativeType = null;
            } else {
              _tmpAlternativeType = _cursor.getString(_cursorIndexOfAlternativeType);
            }
            final double _tmpSavingsAmount;
            _tmpSavingsAmount = _cursor.getDouble(_cursorIndexOfSavingsAmount);
            final double _tmpSavingsPercentage;
            _tmpSavingsPercentage = _cursor.getDouble(_cursorIndexOfSavingsPercentage);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpPros;
            if (_cursor.isNull(_cursorIndexOfPros)) {
              _tmpPros = null;
            } else {
              _tmpPros = _cursor.getString(_cursorIndexOfPros);
            }
            final String _tmpCons;
            if (_cursor.isNull(_cursorIndexOfCons)) {
              _tmpCons = null;
            } else {
              _tmpCons = _cursor.getString(_cursorIndexOfCons);
            }
            final String _tmpAvailabilityInfo;
            if (_cursor.isNull(_cursorIndexOfAvailabilityInfo)) {
              _tmpAvailabilityInfo = null;
            } else {
              _tmpAvailabilityInfo = _cursor.getString(_cursorIndexOfAvailabilityInfo);
            }
            final Integer _tmpQualityRating;
            if (_cursor.isNull(_cursorIndexOfQualityRating)) {
              _tmpQualityRating = null;
            } else {
              _tmpQualityRating = _cursor.getInt(_cursorIndexOfQualityRating);
            }
            final Integer _tmpUserRating;
            if (_cursor.isNull(_cursorIndexOfUserRating)) {
              _tmpUserRating = null;
            } else {
              _tmpUserRating = _cursor.getInt(_cursorIndexOfUserRating);
            }
            final String _tmpSuggestionSource;
            if (_cursor.isNull(_cursorIndexOfSuggestionSource)) {
              _tmpSuggestionSource = null;
            } else {
              _tmpSuggestionSource = _cursor.getString(_cursorIndexOfSuggestionSource);
            }
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final LocalDateTime _tmpCreatedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCreatedDate);
            }
            _tmpCreatedDate = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpLastSuggested;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfLastSuggested)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfLastSuggested);
            }
            _tmpLastSuggested = __converters.toLocalDateTime(_tmp_1);
            final int _tmpTimesShown;
            _tmpTimesShown = _cursor.getInt(_cursorIndexOfTimesShown);
            final int _tmpTimesAccepted;
            _tmpTimesAccepted = _cursor.getInt(_cursorIndexOfTimesAccepted);
            final int _tmpTimesRejected;
            _tmpTimesRejected = _cursor.getInt(_cursorIndexOfTimesRejected);
            final String _tmpUserFeedback;
            if (_cursor.isNull(_cursorIndexOfUserFeedback)) {
              _tmpUserFeedback = null;
            } else {
              _tmpUserFeedback = _cursor.getString(_cursorIndexOfUserFeedback);
            }
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final String _tmpImageUrl;
            if (_cursor.isNull(_cursorIndexOfImageUrl)) {
              _tmpImageUrl = null;
            } else {
              _tmpImageUrl = _cursor.getString(_cursorIndexOfImageUrl);
            }
            final String _tmpProductUrl;
            if (_cursor.isNull(_cursorIndexOfProductUrl)) {
              _tmpProductUrl = null;
            } else {
              _tmpProductUrl = _cursor.getString(_cursorIndexOfProductUrl);
            }
            final String _tmpMerchant;
            if (_cursor.isNull(_cursorIndexOfMerchant)) {
              _tmpMerchant = null;
            } else {
              _tmpMerchant = _cursor.getString(_cursorIndexOfMerchant);
            }
            final String _tmpEstimatedDeliveryTime;
            if (_cursor.isNull(_cursorIndexOfEstimatedDeliveryTime)) {
              _tmpEstimatedDeliveryTime = null;
            } else {
              _tmpEstimatedDeliveryTime = _cursor.getString(_cursorIndexOfEstimatedDeliveryTime);
            }
            final Integer _tmpSustainabilityScore;
            if (_cursor.isNull(_cursorIndexOfSustainabilityScore)) {
              _tmpSustainabilityScore = null;
            } else {
              _tmpSustainabilityScore = _cursor.getInt(_cursorIndexOfSustainabilityScore);
            }
            final String _tmpDifficultyLevel;
            if (_cursor.isNull(_cursorIndexOfDifficultyLevel)) {
              _tmpDifficultyLevel = null;
            } else {
              _tmpDifficultyLevel = _cursor.getString(_cursorIndexOfDifficultyLevel);
            }
            final String _tmpTimeInvestment;
            if (_cursor.isNull(_cursorIndexOfTimeInvestment)) {
              _tmpTimeInvestment = null;
            } else {
              _tmpTimeInvestment = _cursor.getString(_cursorIndexOfTimeInvestment);
            }
            final String _tmpRequiredSkills;
            if (_cursor.isNull(_cursorIndexOfRequiredSkills)) {
              _tmpRequiredSkills = null;
            } else {
              _tmpRequiredSkills = _cursor.getString(_cursorIndexOfRequiredSkills);
            }
            final Double _tmpSuccessRate;
            if (_cursor.isNull(_cursorIndexOfSuccessRate)) {
              _tmpSuccessRate = null;
            } else {
              _tmpSuccessRate = _cursor.getDouble(_cursorIndexOfSuccessRate);
            }
            final String _tmpRelatedAlternatives;
            if (_cursor.isNull(_cursorIndexOfRelatedAlternatives)) {
              _tmpRelatedAlternatives = null;
            } else {
              _tmpRelatedAlternatives = _cursor.getString(_cursorIndexOfRelatedAlternatives);
            }
            _result = new AlternativeProduct(_tmpId,_tmpOriginalProductName,_tmpOriginalPrice,_tmpOriginalCategory,_tmpAlternativeName,_tmpAlternativePrice,_tmpAlternativeCategory,_tmpAlternativeType,_tmpSavingsAmount,_tmpSavingsPercentage,_tmpDescription,_tmpPros,_tmpCons,_tmpAvailabilityInfo,_tmpQualityRating,_tmpUserRating,_tmpSuggestionSource,_tmpConfidenceScore,_tmpCreatedDate,_tmpLastSuggested,_tmpTimesShown,_tmpTimesAccepted,_tmpTimesRejected,_tmpUserFeedback,_tmpIsActive,_tmpTags,_tmpImageUrl,_tmpProductUrl,_tmpMerchant,_tmpEstimatedDeliveryTime,_tmpSustainabilityScore,_tmpDifficultyLevel,_tmpTimeInvestment,_tmpRequiredSkills,_tmpSuccessRate,_tmpRelatedAlternatives);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTopAlternatives(final double minConfidence, final int limit,
      final Continuation<? super List<AlternativeProduct>> $completion) {
    final String _sql = "SELECT * FROM alternative_products WHERE confidenceScore >= ? AND isActive = 1 ORDER BY confidenceScore DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindDouble(_argIndex, minConfidence);
    _argIndex = 2;
    _statement.bindLong(_argIndex, limit);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<AlternativeProduct>>() {
      @Override
      @NonNull
      public List<AlternativeProduct> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfOriginalProductName = CursorUtil.getColumnIndexOrThrow(_cursor, "originalProductName");
          final int _cursorIndexOfOriginalPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "originalPrice");
          final int _cursorIndexOfOriginalCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "originalCategory");
          final int _cursorIndexOfAlternativeName = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeName");
          final int _cursorIndexOfAlternativePrice = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativePrice");
          final int _cursorIndexOfAlternativeCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeCategory");
          final int _cursorIndexOfAlternativeType = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeType");
          final int _cursorIndexOfSavingsAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "savingsAmount");
          final int _cursorIndexOfSavingsPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "savingsPercentage");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfPros = CursorUtil.getColumnIndexOrThrow(_cursor, "pros");
          final int _cursorIndexOfCons = CursorUtil.getColumnIndexOrThrow(_cursor, "cons");
          final int _cursorIndexOfAvailabilityInfo = CursorUtil.getColumnIndexOrThrow(_cursor, "availabilityInfo");
          final int _cursorIndexOfQualityRating = CursorUtil.getColumnIndexOrThrow(_cursor, "qualityRating");
          final int _cursorIndexOfUserRating = CursorUtil.getColumnIndexOrThrow(_cursor, "userRating");
          final int _cursorIndexOfSuggestionSource = CursorUtil.getColumnIndexOrThrow(_cursor, "suggestionSource");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfCreatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "createdDate");
          final int _cursorIndexOfLastSuggested = CursorUtil.getColumnIndexOrThrow(_cursor, "lastSuggested");
          final int _cursorIndexOfTimesShown = CursorUtil.getColumnIndexOrThrow(_cursor, "timesShown");
          final int _cursorIndexOfTimesAccepted = CursorUtil.getColumnIndexOrThrow(_cursor, "timesAccepted");
          final int _cursorIndexOfTimesRejected = CursorUtil.getColumnIndexOrThrow(_cursor, "timesRejected");
          final int _cursorIndexOfUserFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "userFeedback");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUrl");
          final int _cursorIndexOfProductUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "productUrl");
          final int _cursorIndexOfMerchant = CursorUtil.getColumnIndexOrThrow(_cursor, "merchant");
          final int _cursorIndexOfEstimatedDeliveryTime = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedDeliveryTime");
          final int _cursorIndexOfSustainabilityScore = CursorUtil.getColumnIndexOrThrow(_cursor, "sustainabilityScore");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfTimeInvestment = CursorUtil.getColumnIndexOrThrow(_cursor, "timeInvestment");
          final int _cursorIndexOfRequiredSkills = CursorUtil.getColumnIndexOrThrow(_cursor, "requiredSkills");
          final int _cursorIndexOfSuccessRate = CursorUtil.getColumnIndexOrThrow(_cursor, "successRate");
          final int _cursorIndexOfRelatedAlternatives = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedAlternatives");
          final List<AlternativeProduct> _result = new ArrayList<AlternativeProduct>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AlternativeProduct _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpOriginalProductName;
            if (_cursor.isNull(_cursorIndexOfOriginalProductName)) {
              _tmpOriginalProductName = null;
            } else {
              _tmpOriginalProductName = _cursor.getString(_cursorIndexOfOriginalProductName);
            }
            final double _tmpOriginalPrice;
            _tmpOriginalPrice = _cursor.getDouble(_cursorIndexOfOriginalPrice);
            final String _tmpOriginalCategory;
            if (_cursor.isNull(_cursorIndexOfOriginalCategory)) {
              _tmpOriginalCategory = null;
            } else {
              _tmpOriginalCategory = _cursor.getString(_cursorIndexOfOriginalCategory);
            }
            final String _tmpAlternativeName;
            if (_cursor.isNull(_cursorIndexOfAlternativeName)) {
              _tmpAlternativeName = null;
            } else {
              _tmpAlternativeName = _cursor.getString(_cursorIndexOfAlternativeName);
            }
            final double _tmpAlternativePrice;
            _tmpAlternativePrice = _cursor.getDouble(_cursorIndexOfAlternativePrice);
            final String _tmpAlternativeCategory;
            if (_cursor.isNull(_cursorIndexOfAlternativeCategory)) {
              _tmpAlternativeCategory = null;
            } else {
              _tmpAlternativeCategory = _cursor.getString(_cursorIndexOfAlternativeCategory);
            }
            final String _tmpAlternativeType;
            if (_cursor.isNull(_cursorIndexOfAlternativeType)) {
              _tmpAlternativeType = null;
            } else {
              _tmpAlternativeType = _cursor.getString(_cursorIndexOfAlternativeType);
            }
            final double _tmpSavingsAmount;
            _tmpSavingsAmount = _cursor.getDouble(_cursorIndexOfSavingsAmount);
            final double _tmpSavingsPercentage;
            _tmpSavingsPercentage = _cursor.getDouble(_cursorIndexOfSavingsPercentage);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpPros;
            if (_cursor.isNull(_cursorIndexOfPros)) {
              _tmpPros = null;
            } else {
              _tmpPros = _cursor.getString(_cursorIndexOfPros);
            }
            final String _tmpCons;
            if (_cursor.isNull(_cursorIndexOfCons)) {
              _tmpCons = null;
            } else {
              _tmpCons = _cursor.getString(_cursorIndexOfCons);
            }
            final String _tmpAvailabilityInfo;
            if (_cursor.isNull(_cursorIndexOfAvailabilityInfo)) {
              _tmpAvailabilityInfo = null;
            } else {
              _tmpAvailabilityInfo = _cursor.getString(_cursorIndexOfAvailabilityInfo);
            }
            final Integer _tmpQualityRating;
            if (_cursor.isNull(_cursorIndexOfQualityRating)) {
              _tmpQualityRating = null;
            } else {
              _tmpQualityRating = _cursor.getInt(_cursorIndexOfQualityRating);
            }
            final Integer _tmpUserRating;
            if (_cursor.isNull(_cursorIndexOfUserRating)) {
              _tmpUserRating = null;
            } else {
              _tmpUserRating = _cursor.getInt(_cursorIndexOfUserRating);
            }
            final String _tmpSuggestionSource;
            if (_cursor.isNull(_cursorIndexOfSuggestionSource)) {
              _tmpSuggestionSource = null;
            } else {
              _tmpSuggestionSource = _cursor.getString(_cursorIndexOfSuggestionSource);
            }
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final LocalDateTime _tmpCreatedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCreatedDate);
            }
            _tmpCreatedDate = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpLastSuggested;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfLastSuggested)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfLastSuggested);
            }
            _tmpLastSuggested = __converters.toLocalDateTime(_tmp_1);
            final int _tmpTimesShown;
            _tmpTimesShown = _cursor.getInt(_cursorIndexOfTimesShown);
            final int _tmpTimesAccepted;
            _tmpTimesAccepted = _cursor.getInt(_cursorIndexOfTimesAccepted);
            final int _tmpTimesRejected;
            _tmpTimesRejected = _cursor.getInt(_cursorIndexOfTimesRejected);
            final String _tmpUserFeedback;
            if (_cursor.isNull(_cursorIndexOfUserFeedback)) {
              _tmpUserFeedback = null;
            } else {
              _tmpUserFeedback = _cursor.getString(_cursorIndexOfUserFeedback);
            }
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final String _tmpImageUrl;
            if (_cursor.isNull(_cursorIndexOfImageUrl)) {
              _tmpImageUrl = null;
            } else {
              _tmpImageUrl = _cursor.getString(_cursorIndexOfImageUrl);
            }
            final String _tmpProductUrl;
            if (_cursor.isNull(_cursorIndexOfProductUrl)) {
              _tmpProductUrl = null;
            } else {
              _tmpProductUrl = _cursor.getString(_cursorIndexOfProductUrl);
            }
            final String _tmpMerchant;
            if (_cursor.isNull(_cursorIndexOfMerchant)) {
              _tmpMerchant = null;
            } else {
              _tmpMerchant = _cursor.getString(_cursorIndexOfMerchant);
            }
            final String _tmpEstimatedDeliveryTime;
            if (_cursor.isNull(_cursorIndexOfEstimatedDeliveryTime)) {
              _tmpEstimatedDeliveryTime = null;
            } else {
              _tmpEstimatedDeliveryTime = _cursor.getString(_cursorIndexOfEstimatedDeliveryTime);
            }
            final Integer _tmpSustainabilityScore;
            if (_cursor.isNull(_cursorIndexOfSustainabilityScore)) {
              _tmpSustainabilityScore = null;
            } else {
              _tmpSustainabilityScore = _cursor.getInt(_cursorIndexOfSustainabilityScore);
            }
            final String _tmpDifficultyLevel;
            if (_cursor.isNull(_cursorIndexOfDifficultyLevel)) {
              _tmpDifficultyLevel = null;
            } else {
              _tmpDifficultyLevel = _cursor.getString(_cursorIndexOfDifficultyLevel);
            }
            final String _tmpTimeInvestment;
            if (_cursor.isNull(_cursorIndexOfTimeInvestment)) {
              _tmpTimeInvestment = null;
            } else {
              _tmpTimeInvestment = _cursor.getString(_cursorIndexOfTimeInvestment);
            }
            final String _tmpRequiredSkills;
            if (_cursor.isNull(_cursorIndexOfRequiredSkills)) {
              _tmpRequiredSkills = null;
            } else {
              _tmpRequiredSkills = _cursor.getString(_cursorIndexOfRequiredSkills);
            }
            final Double _tmpSuccessRate;
            if (_cursor.isNull(_cursorIndexOfSuccessRate)) {
              _tmpSuccessRate = null;
            } else {
              _tmpSuccessRate = _cursor.getDouble(_cursorIndexOfSuccessRate);
            }
            final String _tmpRelatedAlternatives;
            if (_cursor.isNull(_cursorIndexOfRelatedAlternatives)) {
              _tmpRelatedAlternatives = null;
            } else {
              _tmpRelatedAlternatives = _cursor.getString(_cursorIndexOfRelatedAlternatives);
            }
            _item = new AlternativeProduct(_tmpId,_tmpOriginalProductName,_tmpOriginalPrice,_tmpOriginalCategory,_tmpAlternativeName,_tmpAlternativePrice,_tmpAlternativeCategory,_tmpAlternativeType,_tmpSavingsAmount,_tmpSavingsPercentage,_tmpDescription,_tmpPros,_tmpCons,_tmpAvailabilityInfo,_tmpQualityRating,_tmpUserRating,_tmpSuggestionSource,_tmpConfidenceScore,_tmpCreatedDate,_tmpLastSuggested,_tmpTimesShown,_tmpTimesAccepted,_tmpTimesRejected,_tmpUserFeedback,_tmpIsActive,_tmpTags,_tmpImageUrl,_tmpProductUrl,_tmpMerchant,_tmpEstimatedDeliveryTime,_tmpSustainabilityScore,_tmpDifficultyLevel,_tmpTimeInvestment,_tmpRequiredSkills,_tmpSuccessRate,_tmpRelatedAlternatives);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getActiveAlternativeCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM alternative_products WHERE isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAverageSavings(final Continuation<? super Double> $completion) {
    final String _sql = "SELECT AVG(savingsAmount) FROM alternative_products WHERE isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalSavingsRealized(final Continuation<? super Double> $completion) {
    final String _sql = "SELECT SUM(savingsAmount * timesAccepted) FROM alternative_products WHERE isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAverageUserRating(final Continuation<? super Double> $completion) {
    final String _sql = "SELECT AVG(userRating) FROM alternative_products WHERE userRating IS NOT NULL";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
