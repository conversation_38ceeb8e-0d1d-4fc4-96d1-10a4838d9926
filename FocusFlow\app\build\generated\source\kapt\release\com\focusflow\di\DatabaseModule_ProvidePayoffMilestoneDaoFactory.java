package com.focusflow.di;

import com.focusflow.data.dao.PayoffMilestoneDao;
import com.focusflow.data.database.FocusFlowDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvidePayoffMilestoneDaoFactory implements Factory<PayoffMilestoneDao> {
  private final Provider<FocusFlowDatabase> databaseProvider;

  public DatabaseModule_ProvidePayoffMilestoneDaoFactory(
      Provider<FocusFlowDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public PayoffMilestoneDao get() {
    return providePayoffMilestoneDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvidePayoffMilestoneDaoFactory create(
      Provider<FocusFlowDatabase> databaseProvider) {
    return new DatabaseModule_ProvidePayoffMilestoneDaoFactory(databaseProvider);
  }

  public static PayoffMilestoneDao providePayoffMilestoneDao(FocusFlowDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.providePayoffMilestoneDao(database));
  }
}
