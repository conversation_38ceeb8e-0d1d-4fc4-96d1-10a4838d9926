package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\'\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\u0006\u0010\u0012\u001a\u00020\u0013J\u0012\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0017H\u0002J\u000e\u0010\u0018\u001a\u00020\u00132\u0006\u0010\u0019\u001a\u00020\u0015R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\r0\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001a"}, d2 = {"Lcom/focusflow/ui/viewmodel/MainViewModel;", "Landroidx/lifecycle/ViewModel;", "userPreferencesRepository", "Lcom/focusflow/data/repository/UserPreferencesRepository;", "gamificationService", "Lcom/focusflow/service/GamificationService;", "notificationRepository", "Lcom/focusflow/data/repository/NotificationRepository;", "sampleDataService", "Lcom/focusflow/service/SampleDataService;", "(Lcom/focusflow/data/repository/UserPreferencesRepository;Lcom/focusflow/service/GamificationService;Lcom/focusflow/data/repository/NotificationRepository;Lcom/focusflow/service/SampleDataService;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/focusflow/ui/viewmodel/MainUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "checkOnboardingStatus", "", "parseThemeMode", "Lcom/focusflow/ui/theme/ThemeMode;", "themePreference", "", "updateTheme", "themeMode", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class MainViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.service.GamificationService gamificationService = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.NotificationRepository notificationRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.service.SampleDataService sampleDataService = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.ui.viewmodel.MainUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.MainUiState> uiState = null;
    
    @javax.inject.Inject
    public MainViewModel(@org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.service.GamificationService gamificationService, @org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.NotificationRepository notificationRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.service.SampleDataService sampleDataService) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.MainUiState> getUiState() {
        return null;
    }
    
    public final void checkOnboardingStatus() {
    }
    
    public final void updateTheme(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.theme.ThemeMode themeMode) {
    }
    
    private final com.focusflow.ui.theme.ThemeMode parseThemeMode(java.lang.String themePreference) {
        return null;
    }
}