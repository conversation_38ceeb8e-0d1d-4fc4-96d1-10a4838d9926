{"logs": [{"outputFile": "com.focusflow.app-mergeDebugResources-69:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\Downloads\\Understanding Content in pasted_content (1)\\FocusFlow\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "140,694", "endLines": "12,17", "endColumns": "12,12", "endOffsets": "688,871"}, "to": {"startLines": "9,19", "startColumns": "4,4", "startOffsets": "687,1136", "endLines": "18,22", "endColumns": "12,12", "endOffsets": "1131,1313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b7575953e232ac886e0021593ed04f20\\transformed\\appcompat-1.6.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "2,3,4,5,6,7,8,23", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,1318", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,1402"}}]}]}