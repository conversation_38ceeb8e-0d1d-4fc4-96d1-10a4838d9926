package com.focusflow.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\n\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u000b\u001a\u00020\f2\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0014\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u000f0\u000eH\'J\u001c\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u000f0\u000e2\u0006\u0010\b\u001a\u00020\tH\'J\u001c\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00050\u000f2\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0018\u0010\u0012\u001a\u0004\u0018\u00010\u00052\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u0013\u001a\u00020\f2\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u001c\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u000f0\u000e2\u0006\u0010\u0015\u001a\u00020\u0016H\'J\u0016\u0010\u0017\u001a\u00020\t2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001c\u0010\u0018\u001a\u00020\u00032\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00050\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u001aJ\u001e\u0010\u001b\u001a\u00020\u00032\u0006\u0010\u001c\u001a\u00020\t2\u0006\u0010\u001d\u001a\u00020\u0016H\u00a7@\u00a2\u0006\u0002\u0010\u001eJ\u0016\u0010\u001f\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006\u00a8\u0006 "}, d2 = {"Lcom/focusflow/data/dao/PayoffMilestoneDao;", "", "deleteMilestone", "", "milestone", "Lcom/focusflow/data/model/PayoffMilestone;", "(Lcom/focusflow/data/model/PayoffMilestone;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteMilestonesByPlan", "planId", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCompletedMilestoneCount", "", "getCompletedMilestones", "Lkotlinx/coroutines/flow/Flow;", "", "getMilestonesByPlan", "getMilestonesByPlanSync", "getNextMilestone", "getTotalMilestoneCount", "getUpcomingMilestones", "date", "Lkotlinx/datetime/LocalDate;", "insertMilestone", "insertMilestones", "milestones", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "markMilestoneCompleted", "milestoneId", "completedDate", "(JLkotlinx/datetime/LocalDate;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateMilestone", "app_debug"})
@androidx.room.Dao
public abstract interface PayoffMilestoneDao {
    
    @androidx.room.Query(value = "SELECT * FROM payoff_milestones WHERE payoffPlanId = :planId ORDER BY targetDate ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.PayoffMilestone>> getMilestonesByPlan(long planId);
    
    @androidx.room.Query(value = "SELECT * FROM payoff_milestones WHERE payoffPlanId = :planId ORDER BY targetDate ASC")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getMilestonesByPlanSync(long planId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.PayoffMilestone>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM payoff_milestones WHERE isCompleted = 0 AND targetDate <= :date ORDER BY targetDate ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.PayoffMilestone>> getUpcomingMilestones(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate date);
    
    @androidx.room.Query(value = "SELECT * FROM payoff_milestones WHERE isCompleted = 1 ORDER BY completedDate DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.PayoffMilestone>> getCompletedMilestones();
    
    @androidx.room.Query(value = "SELECT * FROM payoff_milestones WHERE payoffPlanId = :planId AND isCompleted = 0 ORDER BY targetDate ASC LIMIT 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getNextMilestone(long planId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.PayoffMilestone> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM payoff_milestones WHERE payoffPlanId = :planId AND isCompleted = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getCompletedMilestoneCount(long planId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM payoff_milestones WHERE payoffPlanId = :planId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalMilestoneCount(long planId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Insert
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertMilestone(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.PayoffMilestone milestone, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Insert
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertMilestones(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.PayoffMilestone> milestones, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateMilestone(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.PayoffMilestone milestone, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteMilestone(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.PayoffMilestone milestone, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM payoff_milestones WHERE payoffPlanId = :planId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteMilestonesByPlan(long planId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE payoff_milestones SET isCompleted = 1, completedDate = :completedDate WHERE id = :milestoneId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object markMilestoneCompleted(long milestoneId, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate completedDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}