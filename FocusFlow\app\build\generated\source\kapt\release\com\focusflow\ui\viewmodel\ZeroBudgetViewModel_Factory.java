package com.focusflow.ui.viewmodel;

import com.focusflow.data.repository.BudgetCategoryRepository;
import com.focusflow.data.repository.ExpenseRepository;
import com.focusflow.data.repository.UserPreferencesRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ZeroBudgetViewModel_Factory implements Factory<ZeroBudgetViewModel> {
  private final Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider;

  private final Provider<ExpenseRepository> expenseRepositoryProvider;

  private final Provider<UserPreferencesRepository> userPreferencesRepositoryProvider;

  public ZeroBudgetViewModel_Factory(
      Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider,
      Provider<ExpenseRepository> expenseRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider) {
    this.budgetCategoryRepositoryProvider = budgetCategoryRepositoryProvider;
    this.expenseRepositoryProvider = expenseRepositoryProvider;
    this.userPreferencesRepositoryProvider = userPreferencesRepositoryProvider;
  }

  @Override
  public ZeroBudgetViewModel get() {
    return newInstance(budgetCategoryRepositoryProvider.get(), expenseRepositoryProvider.get(), userPreferencesRepositoryProvider.get());
  }

  public static ZeroBudgetViewModel_Factory create(
      Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider,
      Provider<ExpenseRepository> expenseRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider) {
    return new ZeroBudgetViewModel_Factory(budgetCategoryRepositoryProvider, expenseRepositoryProvider, userPreferencesRepositoryProvider);
  }

  public static ZeroBudgetViewModel newInstance(BudgetCategoryRepository budgetCategoryRepository,
      ExpenseRepository expenseRepository, UserPreferencesRepository userPreferencesRepository) {
    return new ZeroBudgetViewModel(budgetCategoryRepository, expenseRepository, userPreferencesRepository);
  }
}
