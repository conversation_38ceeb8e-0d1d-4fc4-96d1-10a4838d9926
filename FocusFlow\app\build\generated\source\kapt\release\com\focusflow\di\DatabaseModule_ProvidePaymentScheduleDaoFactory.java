package com.focusflow.di;

import com.focusflow.data.dao.PaymentScheduleDao;
import com.focusflow.data.database.FocusFlowDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvidePaymentScheduleDaoFactory implements Factory<PaymentScheduleDao> {
  private final Provider<FocusFlowDatabase> databaseProvider;

  public DatabaseModule_ProvidePaymentScheduleDaoFactory(
      Provider<FocusFlowDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public PaymentScheduleDao get() {
    return providePaymentScheduleDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvidePaymentScheduleDaoFactory create(
      Provider<FocusFlowDatabase> databaseProvider) {
    return new DatabaseModule_ProvidePaymentScheduleDaoFactory(databaseProvider);
  }

  public static PaymentScheduleDao providePaymentScheduleDao(FocusFlowDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.providePaymentScheduleDao(database));
  }
}
