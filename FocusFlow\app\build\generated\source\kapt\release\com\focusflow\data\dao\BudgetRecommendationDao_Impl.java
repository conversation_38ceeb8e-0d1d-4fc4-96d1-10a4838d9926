package com.focusflow.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.focusflow.data.database.Converters;
import com.focusflow.data.model.BudgetRecommendation;
import java.lang.Boolean;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;
import kotlinx.datetime.LocalDateTime;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class BudgetRecommendationDao_Impl implements BudgetRecommendationDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<BudgetRecommendation> __insertionAdapterOfBudgetRecommendation;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<BudgetRecommendation> __deletionAdapterOfBudgetRecommendation;

  private final EntityDeletionOrUpdateAdapter<BudgetRecommendation> __updateAdapterOfBudgetRecommendation;

  private final SharedSQLiteStatement __preparedStmtOfUpdateRecommendationResponse;

  private final SharedSQLiteStatement __preparedStmtOfDeactivateOldRecommendationsForCategory;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldProcessedRecommendations;

  public BudgetRecommendationDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfBudgetRecommendation = new EntityInsertionAdapter<BudgetRecommendation>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `budget_recommendations` (`id`,`categoryName`,`recommendedAmount`,`currentAmount`,`confidenceScore`,`reasonCode`,`reasonDescription`,`generatedDate`,`isAccepted`,`userFeedback`,`basedOnDays`,`seasonalFactor`,`trendFactor`,`varianceFactor`,`isActive`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final BudgetRecommendation entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getCategoryName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getCategoryName());
        }
        statement.bindDouble(3, entity.getRecommendedAmount());
        statement.bindDouble(4, entity.getCurrentAmount());
        statement.bindDouble(5, entity.getConfidenceScore());
        if (entity.getReasonCode() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getReasonCode());
        }
        if (entity.getReasonDescription() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getReasonDescription());
        }
        final String _tmp = __converters.fromLocalDateTime(entity.getGeneratedDate());
        if (_tmp == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp);
        }
        final Integer _tmp_1 = entity.isAccepted() == null ? null : (entity.isAccepted() ? 1 : 0);
        if (_tmp_1 == null) {
          statement.bindNull(9);
        } else {
          statement.bindLong(9, _tmp_1);
        }
        if (entity.getUserFeedback() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getUserFeedback());
        }
        statement.bindLong(11, entity.getBasedOnDays());
        statement.bindDouble(12, entity.getSeasonalFactor());
        statement.bindDouble(13, entity.getTrendFactor());
        statement.bindDouble(14, entity.getVarianceFactor());
        final int _tmp_2 = entity.isActive() ? 1 : 0;
        statement.bindLong(15, _tmp_2);
      }
    };
    this.__deletionAdapterOfBudgetRecommendation = new EntityDeletionOrUpdateAdapter<BudgetRecommendation>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `budget_recommendations` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final BudgetRecommendation entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfBudgetRecommendation = new EntityDeletionOrUpdateAdapter<BudgetRecommendation>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `budget_recommendations` SET `id` = ?,`categoryName` = ?,`recommendedAmount` = ?,`currentAmount` = ?,`confidenceScore` = ?,`reasonCode` = ?,`reasonDescription` = ?,`generatedDate` = ?,`isAccepted` = ?,`userFeedback` = ?,`basedOnDays` = ?,`seasonalFactor` = ?,`trendFactor` = ?,`varianceFactor` = ?,`isActive` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final BudgetRecommendation entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getCategoryName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getCategoryName());
        }
        statement.bindDouble(3, entity.getRecommendedAmount());
        statement.bindDouble(4, entity.getCurrentAmount());
        statement.bindDouble(5, entity.getConfidenceScore());
        if (entity.getReasonCode() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getReasonCode());
        }
        if (entity.getReasonDescription() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getReasonDescription());
        }
        final String _tmp = __converters.fromLocalDateTime(entity.getGeneratedDate());
        if (_tmp == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp);
        }
        final Integer _tmp_1 = entity.isAccepted() == null ? null : (entity.isAccepted() ? 1 : 0);
        if (_tmp_1 == null) {
          statement.bindNull(9);
        } else {
          statement.bindLong(9, _tmp_1);
        }
        if (entity.getUserFeedback() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getUserFeedback());
        }
        statement.bindLong(11, entity.getBasedOnDays());
        statement.bindDouble(12, entity.getSeasonalFactor());
        statement.bindDouble(13, entity.getTrendFactor());
        statement.bindDouble(14, entity.getVarianceFactor());
        final int _tmp_2 = entity.isActive() ? 1 : 0;
        statement.bindLong(15, _tmp_2);
        statement.bindLong(16, entity.getId());
      }
    };
    this.__preparedStmtOfUpdateRecommendationResponse = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE budget_recommendations SET isAccepted = ?, userFeedback = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeactivateOldRecommendationsForCategory = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE budget_recommendations SET isActive = 0 WHERE categoryName = ? AND id != ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteOldProcessedRecommendations = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM budget_recommendations WHERE generatedDate < ? AND isAccepted IS NOT NULL";
        return _query;
      }
    };
  }

  @Override
  public Object insertRecommendation(final BudgetRecommendation recommendation,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfBudgetRecommendation.insertAndReturnId(recommendation);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteRecommendation(final BudgetRecommendation recommendation,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfBudgetRecommendation.handle(recommendation);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateRecommendation(final BudgetRecommendation recommendation,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfBudgetRecommendation.handle(recommendation);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateRecommendationResponse(final long id, final boolean isAccepted,
      final String feedback, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateRecommendationResponse.acquire();
        int _argIndex = 1;
        final int _tmp = isAccepted ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 2;
        if (feedback == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, feedback);
        }
        _argIndex = 3;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateRecommendationResponse.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deactivateOldRecommendationsForCategory(final String categoryName,
      final long excludeId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeactivateOldRecommendationsForCategory.acquire();
        int _argIndex = 1;
        if (categoryName == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, categoryName);
        }
        _argIndex = 2;
        _stmt.bindLong(_argIndex, excludeId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeactivateOldRecommendationsForCategory.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteOldProcessedRecommendations(final LocalDateTime cutoffDate,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldProcessedRecommendations.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromLocalDateTime(cutoffDate);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldProcessedRecommendations.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<BudgetRecommendation>> getAllActiveRecommendations() {
    final String _sql = "SELECT * FROM budget_recommendations WHERE isActive = 1 ORDER BY generatedDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"budget_recommendations"}, new Callable<List<BudgetRecommendation>>() {
      @Override
      @NonNull
      public List<BudgetRecommendation> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryName");
          final int _cursorIndexOfRecommendedAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendedAmount");
          final int _cursorIndexOfCurrentAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "currentAmount");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfReasonCode = CursorUtil.getColumnIndexOrThrow(_cursor, "reasonCode");
          final int _cursorIndexOfReasonDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "reasonDescription");
          final int _cursorIndexOfGeneratedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "generatedDate");
          final int _cursorIndexOfIsAccepted = CursorUtil.getColumnIndexOrThrow(_cursor, "isAccepted");
          final int _cursorIndexOfUserFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "userFeedback");
          final int _cursorIndexOfBasedOnDays = CursorUtil.getColumnIndexOrThrow(_cursor, "basedOnDays");
          final int _cursorIndexOfSeasonalFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalFactor");
          final int _cursorIndexOfTrendFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "trendFactor");
          final int _cursorIndexOfVarianceFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "varianceFactor");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final List<BudgetRecommendation> _result = new ArrayList<BudgetRecommendation>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BudgetRecommendation _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCategoryName;
            if (_cursor.isNull(_cursorIndexOfCategoryName)) {
              _tmpCategoryName = null;
            } else {
              _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
            }
            final double _tmpRecommendedAmount;
            _tmpRecommendedAmount = _cursor.getDouble(_cursorIndexOfRecommendedAmount);
            final double _tmpCurrentAmount;
            _tmpCurrentAmount = _cursor.getDouble(_cursorIndexOfCurrentAmount);
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final String _tmpReasonCode;
            if (_cursor.isNull(_cursorIndexOfReasonCode)) {
              _tmpReasonCode = null;
            } else {
              _tmpReasonCode = _cursor.getString(_cursorIndexOfReasonCode);
            }
            final String _tmpReasonDescription;
            if (_cursor.isNull(_cursorIndexOfReasonDescription)) {
              _tmpReasonDescription = null;
            } else {
              _tmpReasonDescription = _cursor.getString(_cursorIndexOfReasonDescription);
            }
            final LocalDateTime _tmpGeneratedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfGeneratedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfGeneratedDate);
            }
            _tmpGeneratedDate = __converters.toLocalDateTime(_tmp);
            final Boolean _tmpIsAccepted;
            final Integer _tmp_1;
            if (_cursor.isNull(_cursorIndexOfIsAccepted)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(_cursorIndexOfIsAccepted);
            }
            _tmpIsAccepted = _tmp_1 == null ? null : _tmp_1 != 0;
            final String _tmpUserFeedback;
            if (_cursor.isNull(_cursorIndexOfUserFeedback)) {
              _tmpUserFeedback = null;
            } else {
              _tmpUserFeedback = _cursor.getString(_cursorIndexOfUserFeedback);
            }
            final int _tmpBasedOnDays;
            _tmpBasedOnDays = _cursor.getInt(_cursorIndexOfBasedOnDays);
            final double _tmpSeasonalFactor;
            _tmpSeasonalFactor = _cursor.getDouble(_cursorIndexOfSeasonalFactor);
            final double _tmpTrendFactor;
            _tmpTrendFactor = _cursor.getDouble(_cursorIndexOfTrendFactor);
            final double _tmpVarianceFactor;
            _tmpVarianceFactor = _cursor.getDouble(_cursorIndexOfVarianceFactor);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            _item = new BudgetRecommendation(_tmpId,_tmpCategoryName,_tmpRecommendedAmount,_tmpCurrentAmount,_tmpConfidenceScore,_tmpReasonCode,_tmpReasonDescription,_tmpGeneratedDate,_tmpIsAccepted,_tmpUserFeedback,_tmpBasedOnDays,_tmpSeasonalFactor,_tmpTrendFactor,_tmpVarianceFactor,_tmpIsActive);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getLatestRecommendationForCategory(final String categoryName,
      final Continuation<? super BudgetRecommendation> $completion) {
    final String _sql = "SELECT * FROM budget_recommendations WHERE categoryName = ? AND isActive = 1 ORDER BY generatedDate DESC LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (categoryName == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, categoryName);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<BudgetRecommendation>() {
      @Override
      @Nullable
      public BudgetRecommendation call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryName");
          final int _cursorIndexOfRecommendedAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendedAmount");
          final int _cursorIndexOfCurrentAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "currentAmount");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfReasonCode = CursorUtil.getColumnIndexOrThrow(_cursor, "reasonCode");
          final int _cursorIndexOfReasonDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "reasonDescription");
          final int _cursorIndexOfGeneratedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "generatedDate");
          final int _cursorIndexOfIsAccepted = CursorUtil.getColumnIndexOrThrow(_cursor, "isAccepted");
          final int _cursorIndexOfUserFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "userFeedback");
          final int _cursorIndexOfBasedOnDays = CursorUtil.getColumnIndexOrThrow(_cursor, "basedOnDays");
          final int _cursorIndexOfSeasonalFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalFactor");
          final int _cursorIndexOfTrendFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "trendFactor");
          final int _cursorIndexOfVarianceFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "varianceFactor");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final BudgetRecommendation _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCategoryName;
            if (_cursor.isNull(_cursorIndexOfCategoryName)) {
              _tmpCategoryName = null;
            } else {
              _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
            }
            final double _tmpRecommendedAmount;
            _tmpRecommendedAmount = _cursor.getDouble(_cursorIndexOfRecommendedAmount);
            final double _tmpCurrentAmount;
            _tmpCurrentAmount = _cursor.getDouble(_cursorIndexOfCurrentAmount);
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final String _tmpReasonCode;
            if (_cursor.isNull(_cursorIndexOfReasonCode)) {
              _tmpReasonCode = null;
            } else {
              _tmpReasonCode = _cursor.getString(_cursorIndexOfReasonCode);
            }
            final String _tmpReasonDescription;
            if (_cursor.isNull(_cursorIndexOfReasonDescription)) {
              _tmpReasonDescription = null;
            } else {
              _tmpReasonDescription = _cursor.getString(_cursorIndexOfReasonDescription);
            }
            final LocalDateTime _tmpGeneratedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfGeneratedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfGeneratedDate);
            }
            _tmpGeneratedDate = __converters.toLocalDateTime(_tmp);
            final Boolean _tmpIsAccepted;
            final Integer _tmp_1;
            if (_cursor.isNull(_cursorIndexOfIsAccepted)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(_cursorIndexOfIsAccepted);
            }
            _tmpIsAccepted = _tmp_1 == null ? null : _tmp_1 != 0;
            final String _tmpUserFeedback;
            if (_cursor.isNull(_cursorIndexOfUserFeedback)) {
              _tmpUserFeedback = null;
            } else {
              _tmpUserFeedback = _cursor.getString(_cursorIndexOfUserFeedback);
            }
            final int _tmpBasedOnDays;
            _tmpBasedOnDays = _cursor.getInt(_cursorIndexOfBasedOnDays);
            final double _tmpSeasonalFactor;
            _tmpSeasonalFactor = _cursor.getDouble(_cursorIndexOfSeasonalFactor);
            final double _tmpTrendFactor;
            _tmpTrendFactor = _cursor.getDouble(_cursorIndexOfTrendFactor);
            final double _tmpVarianceFactor;
            _tmpVarianceFactor = _cursor.getDouble(_cursorIndexOfVarianceFactor);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            _result = new BudgetRecommendation(_tmpId,_tmpCategoryName,_tmpRecommendedAmount,_tmpCurrentAmount,_tmpConfidenceScore,_tmpReasonCode,_tmpReasonDescription,_tmpGeneratedDate,_tmpIsAccepted,_tmpUserFeedback,_tmpBasedOnDays,_tmpSeasonalFactor,_tmpTrendFactor,_tmpVarianceFactor,_tmpIsActive);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<BudgetRecommendation>> getPendingRecommendations() {
    final String _sql = "SELECT * FROM budget_recommendations WHERE isAccepted IS NULL AND isActive = 1 ORDER BY confidenceScore DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"budget_recommendations"}, new Callable<List<BudgetRecommendation>>() {
      @Override
      @NonNull
      public List<BudgetRecommendation> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryName");
          final int _cursorIndexOfRecommendedAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendedAmount");
          final int _cursorIndexOfCurrentAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "currentAmount");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfReasonCode = CursorUtil.getColumnIndexOrThrow(_cursor, "reasonCode");
          final int _cursorIndexOfReasonDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "reasonDescription");
          final int _cursorIndexOfGeneratedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "generatedDate");
          final int _cursorIndexOfIsAccepted = CursorUtil.getColumnIndexOrThrow(_cursor, "isAccepted");
          final int _cursorIndexOfUserFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "userFeedback");
          final int _cursorIndexOfBasedOnDays = CursorUtil.getColumnIndexOrThrow(_cursor, "basedOnDays");
          final int _cursorIndexOfSeasonalFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalFactor");
          final int _cursorIndexOfTrendFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "trendFactor");
          final int _cursorIndexOfVarianceFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "varianceFactor");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final List<BudgetRecommendation> _result = new ArrayList<BudgetRecommendation>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BudgetRecommendation _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCategoryName;
            if (_cursor.isNull(_cursorIndexOfCategoryName)) {
              _tmpCategoryName = null;
            } else {
              _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
            }
            final double _tmpRecommendedAmount;
            _tmpRecommendedAmount = _cursor.getDouble(_cursorIndexOfRecommendedAmount);
            final double _tmpCurrentAmount;
            _tmpCurrentAmount = _cursor.getDouble(_cursorIndexOfCurrentAmount);
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final String _tmpReasonCode;
            if (_cursor.isNull(_cursorIndexOfReasonCode)) {
              _tmpReasonCode = null;
            } else {
              _tmpReasonCode = _cursor.getString(_cursorIndexOfReasonCode);
            }
            final String _tmpReasonDescription;
            if (_cursor.isNull(_cursorIndexOfReasonDescription)) {
              _tmpReasonDescription = null;
            } else {
              _tmpReasonDescription = _cursor.getString(_cursorIndexOfReasonDescription);
            }
            final LocalDateTime _tmpGeneratedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfGeneratedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfGeneratedDate);
            }
            _tmpGeneratedDate = __converters.toLocalDateTime(_tmp);
            final Boolean _tmpIsAccepted;
            final Integer _tmp_1;
            if (_cursor.isNull(_cursorIndexOfIsAccepted)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(_cursorIndexOfIsAccepted);
            }
            _tmpIsAccepted = _tmp_1 == null ? null : _tmp_1 != 0;
            final String _tmpUserFeedback;
            if (_cursor.isNull(_cursorIndexOfUserFeedback)) {
              _tmpUserFeedback = null;
            } else {
              _tmpUserFeedback = _cursor.getString(_cursorIndexOfUserFeedback);
            }
            final int _tmpBasedOnDays;
            _tmpBasedOnDays = _cursor.getInt(_cursorIndexOfBasedOnDays);
            final double _tmpSeasonalFactor;
            _tmpSeasonalFactor = _cursor.getDouble(_cursorIndexOfSeasonalFactor);
            final double _tmpTrendFactor;
            _tmpTrendFactor = _cursor.getDouble(_cursorIndexOfTrendFactor);
            final double _tmpVarianceFactor;
            _tmpVarianceFactor = _cursor.getDouble(_cursorIndexOfVarianceFactor);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            _item = new BudgetRecommendation(_tmpId,_tmpCategoryName,_tmpRecommendedAmount,_tmpCurrentAmount,_tmpConfidenceScore,_tmpReasonCode,_tmpReasonDescription,_tmpGeneratedDate,_tmpIsAccepted,_tmpUserFeedback,_tmpBasedOnDays,_tmpSeasonalFactor,_tmpTrendFactor,_tmpVarianceFactor,_tmpIsActive);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<BudgetRecommendation>> getAcceptedRecommendations() {
    final String _sql = "SELECT * FROM budget_recommendations WHERE isAccepted = 1 ORDER BY generatedDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"budget_recommendations"}, new Callable<List<BudgetRecommendation>>() {
      @Override
      @NonNull
      public List<BudgetRecommendation> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryName");
          final int _cursorIndexOfRecommendedAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendedAmount");
          final int _cursorIndexOfCurrentAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "currentAmount");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfReasonCode = CursorUtil.getColumnIndexOrThrow(_cursor, "reasonCode");
          final int _cursorIndexOfReasonDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "reasonDescription");
          final int _cursorIndexOfGeneratedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "generatedDate");
          final int _cursorIndexOfIsAccepted = CursorUtil.getColumnIndexOrThrow(_cursor, "isAccepted");
          final int _cursorIndexOfUserFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "userFeedback");
          final int _cursorIndexOfBasedOnDays = CursorUtil.getColumnIndexOrThrow(_cursor, "basedOnDays");
          final int _cursorIndexOfSeasonalFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalFactor");
          final int _cursorIndexOfTrendFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "trendFactor");
          final int _cursorIndexOfVarianceFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "varianceFactor");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final List<BudgetRecommendation> _result = new ArrayList<BudgetRecommendation>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BudgetRecommendation _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCategoryName;
            if (_cursor.isNull(_cursorIndexOfCategoryName)) {
              _tmpCategoryName = null;
            } else {
              _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
            }
            final double _tmpRecommendedAmount;
            _tmpRecommendedAmount = _cursor.getDouble(_cursorIndexOfRecommendedAmount);
            final double _tmpCurrentAmount;
            _tmpCurrentAmount = _cursor.getDouble(_cursorIndexOfCurrentAmount);
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final String _tmpReasonCode;
            if (_cursor.isNull(_cursorIndexOfReasonCode)) {
              _tmpReasonCode = null;
            } else {
              _tmpReasonCode = _cursor.getString(_cursorIndexOfReasonCode);
            }
            final String _tmpReasonDescription;
            if (_cursor.isNull(_cursorIndexOfReasonDescription)) {
              _tmpReasonDescription = null;
            } else {
              _tmpReasonDescription = _cursor.getString(_cursorIndexOfReasonDescription);
            }
            final LocalDateTime _tmpGeneratedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfGeneratedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfGeneratedDate);
            }
            _tmpGeneratedDate = __converters.toLocalDateTime(_tmp);
            final Boolean _tmpIsAccepted;
            final Integer _tmp_1;
            if (_cursor.isNull(_cursorIndexOfIsAccepted)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(_cursorIndexOfIsAccepted);
            }
            _tmpIsAccepted = _tmp_1 == null ? null : _tmp_1 != 0;
            final String _tmpUserFeedback;
            if (_cursor.isNull(_cursorIndexOfUserFeedback)) {
              _tmpUserFeedback = null;
            } else {
              _tmpUserFeedback = _cursor.getString(_cursorIndexOfUserFeedback);
            }
            final int _tmpBasedOnDays;
            _tmpBasedOnDays = _cursor.getInt(_cursorIndexOfBasedOnDays);
            final double _tmpSeasonalFactor;
            _tmpSeasonalFactor = _cursor.getDouble(_cursorIndexOfSeasonalFactor);
            final double _tmpTrendFactor;
            _tmpTrendFactor = _cursor.getDouble(_cursorIndexOfTrendFactor);
            final double _tmpVarianceFactor;
            _tmpVarianceFactor = _cursor.getDouble(_cursorIndexOfVarianceFactor);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            _item = new BudgetRecommendation(_tmpId,_tmpCategoryName,_tmpRecommendedAmount,_tmpCurrentAmount,_tmpConfidenceScore,_tmpReasonCode,_tmpReasonDescription,_tmpGeneratedDate,_tmpIsAccepted,_tmpUserFeedback,_tmpBasedOnDays,_tmpSeasonalFactor,_tmpTrendFactor,_tmpVarianceFactor,_tmpIsActive);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<BudgetRecommendation>> getRejectedRecommendations() {
    final String _sql = "SELECT * FROM budget_recommendations WHERE isAccepted = 0 ORDER BY generatedDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"budget_recommendations"}, new Callable<List<BudgetRecommendation>>() {
      @Override
      @NonNull
      public List<BudgetRecommendation> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryName");
          final int _cursorIndexOfRecommendedAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendedAmount");
          final int _cursorIndexOfCurrentAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "currentAmount");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfReasonCode = CursorUtil.getColumnIndexOrThrow(_cursor, "reasonCode");
          final int _cursorIndexOfReasonDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "reasonDescription");
          final int _cursorIndexOfGeneratedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "generatedDate");
          final int _cursorIndexOfIsAccepted = CursorUtil.getColumnIndexOrThrow(_cursor, "isAccepted");
          final int _cursorIndexOfUserFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "userFeedback");
          final int _cursorIndexOfBasedOnDays = CursorUtil.getColumnIndexOrThrow(_cursor, "basedOnDays");
          final int _cursorIndexOfSeasonalFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalFactor");
          final int _cursorIndexOfTrendFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "trendFactor");
          final int _cursorIndexOfVarianceFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "varianceFactor");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final List<BudgetRecommendation> _result = new ArrayList<BudgetRecommendation>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BudgetRecommendation _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCategoryName;
            if (_cursor.isNull(_cursorIndexOfCategoryName)) {
              _tmpCategoryName = null;
            } else {
              _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
            }
            final double _tmpRecommendedAmount;
            _tmpRecommendedAmount = _cursor.getDouble(_cursorIndexOfRecommendedAmount);
            final double _tmpCurrentAmount;
            _tmpCurrentAmount = _cursor.getDouble(_cursorIndexOfCurrentAmount);
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final String _tmpReasonCode;
            if (_cursor.isNull(_cursorIndexOfReasonCode)) {
              _tmpReasonCode = null;
            } else {
              _tmpReasonCode = _cursor.getString(_cursorIndexOfReasonCode);
            }
            final String _tmpReasonDescription;
            if (_cursor.isNull(_cursorIndexOfReasonDescription)) {
              _tmpReasonDescription = null;
            } else {
              _tmpReasonDescription = _cursor.getString(_cursorIndexOfReasonDescription);
            }
            final LocalDateTime _tmpGeneratedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfGeneratedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfGeneratedDate);
            }
            _tmpGeneratedDate = __converters.toLocalDateTime(_tmp);
            final Boolean _tmpIsAccepted;
            final Integer _tmp_1;
            if (_cursor.isNull(_cursorIndexOfIsAccepted)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(_cursorIndexOfIsAccepted);
            }
            _tmpIsAccepted = _tmp_1 == null ? null : _tmp_1 != 0;
            final String _tmpUserFeedback;
            if (_cursor.isNull(_cursorIndexOfUserFeedback)) {
              _tmpUserFeedback = null;
            } else {
              _tmpUserFeedback = _cursor.getString(_cursorIndexOfUserFeedback);
            }
            final int _tmpBasedOnDays;
            _tmpBasedOnDays = _cursor.getInt(_cursorIndexOfBasedOnDays);
            final double _tmpSeasonalFactor;
            _tmpSeasonalFactor = _cursor.getDouble(_cursorIndexOfSeasonalFactor);
            final double _tmpTrendFactor;
            _tmpTrendFactor = _cursor.getDouble(_cursorIndexOfTrendFactor);
            final double _tmpVarianceFactor;
            _tmpVarianceFactor = _cursor.getDouble(_cursorIndexOfVarianceFactor);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            _item = new BudgetRecommendation(_tmpId,_tmpCategoryName,_tmpRecommendedAmount,_tmpCurrentAmount,_tmpConfidenceScore,_tmpReasonCode,_tmpReasonDescription,_tmpGeneratedDate,_tmpIsAccepted,_tmpUserFeedback,_tmpBasedOnDays,_tmpSeasonalFactor,_tmpTrendFactor,_tmpVarianceFactor,_tmpIsActive);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<BudgetRecommendation>> getRecommendationsByReason(final String reasonCode) {
    final String _sql = "SELECT * FROM budget_recommendations WHERE reasonCode = ? AND isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (reasonCode == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, reasonCode);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"budget_recommendations"}, new Callable<List<BudgetRecommendation>>() {
      @Override
      @NonNull
      public List<BudgetRecommendation> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryName");
          final int _cursorIndexOfRecommendedAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendedAmount");
          final int _cursorIndexOfCurrentAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "currentAmount");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfReasonCode = CursorUtil.getColumnIndexOrThrow(_cursor, "reasonCode");
          final int _cursorIndexOfReasonDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "reasonDescription");
          final int _cursorIndexOfGeneratedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "generatedDate");
          final int _cursorIndexOfIsAccepted = CursorUtil.getColumnIndexOrThrow(_cursor, "isAccepted");
          final int _cursorIndexOfUserFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "userFeedback");
          final int _cursorIndexOfBasedOnDays = CursorUtil.getColumnIndexOrThrow(_cursor, "basedOnDays");
          final int _cursorIndexOfSeasonalFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalFactor");
          final int _cursorIndexOfTrendFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "trendFactor");
          final int _cursorIndexOfVarianceFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "varianceFactor");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final List<BudgetRecommendation> _result = new ArrayList<BudgetRecommendation>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BudgetRecommendation _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCategoryName;
            if (_cursor.isNull(_cursorIndexOfCategoryName)) {
              _tmpCategoryName = null;
            } else {
              _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
            }
            final double _tmpRecommendedAmount;
            _tmpRecommendedAmount = _cursor.getDouble(_cursorIndexOfRecommendedAmount);
            final double _tmpCurrentAmount;
            _tmpCurrentAmount = _cursor.getDouble(_cursorIndexOfCurrentAmount);
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final String _tmpReasonCode;
            if (_cursor.isNull(_cursorIndexOfReasonCode)) {
              _tmpReasonCode = null;
            } else {
              _tmpReasonCode = _cursor.getString(_cursorIndexOfReasonCode);
            }
            final String _tmpReasonDescription;
            if (_cursor.isNull(_cursorIndexOfReasonDescription)) {
              _tmpReasonDescription = null;
            } else {
              _tmpReasonDescription = _cursor.getString(_cursorIndexOfReasonDescription);
            }
            final LocalDateTime _tmpGeneratedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfGeneratedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfGeneratedDate);
            }
            _tmpGeneratedDate = __converters.toLocalDateTime(_tmp);
            final Boolean _tmpIsAccepted;
            final Integer _tmp_1;
            if (_cursor.isNull(_cursorIndexOfIsAccepted)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(_cursorIndexOfIsAccepted);
            }
            _tmpIsAccepted = _tmp_1 == null ? null : _tmp_1 != 0;
            final String _tmpUserFeedback;
            if (_cursor.isNull(_cursorIndexOfUserFeedback)) {
              _tmpUserFeedback = null;
            } else {
              _tmpUserFeedback = _cursor.getString(_cursorIndexOfUserFeedback);
            }
            final int _tmpBasedOnDays;
            _tmpBasedOnDays = _cursor.getInt(_cursorIndexOfBasedOnDays);
            final double _tmpSeasonalFactor;
            _tmpSeasonalFactor = _cursor.getDouble(_cursorIndexOfSeasonalFactor);
            final double _tmpTrendFactor;
            _tmpTrendFactor = _cursor.getDouble(_cursorIndexOfTrendFactor);
            final double _tmpVarianceFactor;
            _tmpVarianceFactor = _cursor.getDouble(_cursorIndexOfVarianceFactor);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            _item = new BudgetRecommendation(_tmpId,_tmpCategoryName,_tmpRecommendedAmount,_tmpCurrentAmount,_tmpConfidenceScore,_tmpReasonCode,_tmpReasonDescription,_tmpGeneratedDate,_tmpIsAccepted,_tmpUserFeedback,_tmpBasedOnDays,_tmpSeasonalFactor,_tmpTrendFactor,_tmpVarianceFactor,_tmpIsActive);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<BudgetRecommendation>> getHighConfidenceRecommendations(
      final double minConfidence) {
    final String _sql = "SELECT * FROM budget_recommendations WHERE confidenceScore >= ? AND isActive = 1 ORDER BY confidenceScore DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindDouble(_argIndex, minConfidence);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"budget_recommendations"}, new Callable<List<BudgetRecommendation>>() {
      @Override
      @NonNull
      public List<BudgetRecommendation> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryName");
          final int _cursorIndexOfRecommendedAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendedAmount");
          final int _cursorIndexOfCurrentAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "currentAmount");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfReasonCode = CursorUtil.getColumnIndexOrThrow(_cursor, "reasonCode");
          final int _cursorIndexOfReasonDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "reasonDescription");
          final int _cursorIndexOfGeneratedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "generatedDate");
          final int _cursorIndexOfIsAccepted = CursorUtil.getColumnIndexOrThrow(_cursor, "isAccepted");
          final int _cursorIndexOfUserFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "userFeedback");
          final int _cursorIndexOfBasedOnDays = CursorUtil.getColumnIndexOrThrow(_cursor, "basedOnDays");
          final int _cursorIndexOfSeasonalFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalFactor");
          final int _cursorIndexOfTrendFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "trendFactor");
          final int _cursorIndexOfVarianceFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "varianceFactor");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final List<BudgetRecommendation> _result = new ArrayList<BudgetRecommendation>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BudgetRecommendation _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCategoryName;
            if (_cursor.isNull(_cursorIndexOfCategoryName)) {
              _tmpCategoryName = null;
            } else {
              _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
            }
            final double _tmpRecommendedAmount;
            _tmpRecommendedAmount = _cursor.getDouble(_cursorIndexOfRecommendedAmount);
            final double _tmpCurrentAmount;
            _tmpCurrentAmount = _cursor.getDouble(_cursorIndexOfCurrentAmount);
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final String _tmpReasonCode;
            if (_cursor.isNull(_cursorIndexOfReasonCode)) {
              _tmpReasonCode = null;
            } else {
              _tmpReasonCode = _cursor.getString(_cursorIndexOfReasonCode);
            }
            final String _tmpReasonDescription;
            if (_cursor.isNull(_cursorIndexOfReasonDescription)) {
              _tmpReasonDescription = null;
            } else {
              _tmpReasonDescription = _cursor.getString(_cursorIndexOfReasonDescription);
            }
            final LocalDateTime _tmpGeneratedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfGeneratedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfGeneratedDate);
            }
            _tmpGeneratedDate = __converters.toLocalDateTime(_tmp);
            final Boolean _tmpIsAccepted;
            final Integer _tmp_1;
            if (_cursor.isNull(_cursorIndexOfIsAccepted)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(_cursorIndexOfIsAccepted);
            }
            _tmpIsAccepted = _tmp_1 == null ? null : _tmp_1 != 0;
            final String _tmpUserFeedback;
            if (_cursor.isNull(_cursorIndexOfUserFeedback)) {
              _tmpUserFeedback = null;
            } else {
              _tmpUserFeedback = _cursor.getString(_cursorIndexOfUserFeedback);
            }
            final int _tmpBasedOnDays;
            _tmpBasedOnDays = _cursor.getInt(_cursorIndexOfBasedOnDays);
            final double _tmpSeasonalFactor;
            _tmpSeasonalFactor = _cursor.getDouble(_cursorIndexOfSeasonalFactor);
            final double _tmpTrendFactor;
            _tmpTrendFactor = _cursor.getDouble(_cursorIndexOfTrendFactor);
            final double _tmpVarianceFactor;
            _tmpVarianceFactor = _cursor.getDouble(_cursorIndexOfVarianceFactor);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            _item = new BudgetRecommendation(_tmpId,_tmpCategoryName,_tmpRecommendedAmount,_tmpCurrentAmount,_tmpConfidenceScore,_tmpReasonCode,_tmpReasonDescription,_tmpGeneratedDate,_tmpIsAccepted,_tmpUserFeedback,_tmpBasedOnDays,_tmpSeasonalFactor,_tmpTrendFactor,_tmpVarianceFactor,_tmpIsActive);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getRecommendationById(final long id,
      final Continuation<? super BudgetRecommendation> $completion) {
    final String _sql = "SELECT * FROM budget_recommendations WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<BudgetRecommendation>() {
      @Override
      @Nullable
      public BudgetRecommendation call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryName");
          final int _cursorIndexOfRecommendedAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendedAmount");
          final int _cursorIndexOfCurrentAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "currentAmount");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfReasonCode = CursorUtil.getColumnIndexOrThrow(_cursor, "reasonCode");
          final int _cursorIndexOfReasonDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "reasonDescription");
          final int _cursorIndexOfGeneratedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "generatedDate");
          final int _cursorIndexOfIsAccepted = CursorUtil.getColumnIndexOrThrow(_cursor, "isAccepted");
          final int _cursorIndexOfUserFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "userFeedback");
          final int _cursorIndexOfBasedOnDays = CursorUtil.getColumnIndexOrThrow(_cursor, "basedOnDays");
          final int _cursorIndexOfSeasonalFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalFactor");
          final int _cursorIndexOfTrendFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "trendFactor");
          final int _cursorIndexOfVarianceFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "varianceFactor");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final BudgetRecommendation _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCategoryName;
            if (_cursor.isNull(_cursorIndexOfCategoryName)) {
              _tmpCategoryName = null;
            } else {
              _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
            }
            final double _tmpRecommendedAmount;
            _tmpRecommendedAmount = _cursor.getDouble(_cursorIndexOfRecommendedAmount);
            final double _tmpCurrentAmount;
            _tmpCurrentAmount = _cursor.getDouble(_cursorIndexOfCurrentAmount);
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final String _tmpReasonCode;
            if (_cursor.isNull(_cursorIndexOfReasonCode)) {
              _tmpReasonCode = null;
            } else {
              _tmpReasonCode = _cursor.getString(_cursorIndexOfReasonCode);
            }
            final String _tmpReasonDescription;
            if (_cursor.isNull(_cursorIndexOfReasonDescription)) {
              _tmpReasonDescription = null;
            } else {
              _tmpReasonDescription = _cursor.getString(_cursorIndexOfReasonDescription);
            }
            final LocalDateTime _tmpGeneratedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfGeneratedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfGeneratedDate);
            }
            _tmpGeneratedDate = __converters.toLocalDateTime(_tmp);
            final Boolean _tmpIsAccepted;
            final Integer _tmp_1;
            if (_cursor.isNull(_cursorIndexOfIsAccepted)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(_cursorIndexOfIsAccepted);
            }
            _tmpIsAccepted = _tmp_1 == null ? null : _tmp_1 != 0;
            final String _tmpUserFeedback;
            if (_cursor.isNull(_cursorIndexOfUserFeedback)) {
              _tmpUserFeedback = null;
            } else {
              _tmpUserFeedback = _cursor.getString(_cursorIndexOfUserFeedback);
            }
            final int _tmpBasedOnDays;
            _tmpBasedOnDays = _cursor.getInt(_cursorIndexOfBasedOnDays);
            final double _tmpSeasonalFactor;
            _tmpSeasonalFactor = _cursor.getDouble(_cursorIndexOfSeasonalFactor);
            final double _tmpTrendFactor;
            _tmpTrendFactor = _cursor.getDouble(_cursorIndexOfTrendFactor);
            final double _tmpVarianceFactor;
            _tmpVarianceFactor = _cursor.getDouble(_cursorIndexOfVarianceFactor);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            _result = new BudgetRecommendation(_tmpId,_tmpCategoryName,_tmpRecommendedAmount,_tmpCurrentAmount,_tmpConfidenceScore,_tmpReasonCode,_tmpReasonDescription,_tmpGeneratedDate,_tmpIsAccepted,_tmpUserFeedback,_tmpBasedOnDays,_tmpSeasonalFactor,_tmpTrendFactor,_tmpVarianceFactor,_tmpIsActive);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPendingRecommendationCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM budget_recommendations WHERE isAccepted IS NULL AND isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAverageAcceptedConfidenceScore(final Continuation<? super Double> $completion) {
    final String _sql = "SELECT AVG(confidenceScore) FROM budget_recommendations WHERE isAccepted = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
