package com.focusflow.data.repository;

import com.focusflow.data.dao.WishlistItemDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class WishlistRepository_Factory implements Factory<WishlistRepository> {
  private final Provider<WishlistItemDao> wishlistItemDaoProvider;

  public WishlistRepository_Factory(Provider<WishlistItemDao> wishlistItemDaoProvider) {
    this.wishlistItemDaoProvider = wishlistItemDaoProvider;
  }

  @Override
  public WishlistRepository get() {
    return newInstance(wishlistItemDaoProvider.get());
  }

  public static WishlistRepository_Factory create(
      Provider<WishlistItemDao> wishlistItemDaoProvider) {
    return new WishlistRepository_Factory(wishlistItemDaoProvider);
  }

  public static WishlistRepository newInstance(WishlistItemDao wishlistItemDao) {
    return new WishlistRepository(wishlistItemDao);
  }
}
