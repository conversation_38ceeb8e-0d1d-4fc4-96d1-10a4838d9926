com.focusflow.app-jetified-security-crypto-1.1.0-alpha06-0 C:\Users\<USER>\.gradle\caches\8.12\transforms\02139e6724a710870761a13124ce4c86\transformed\jetified-security-crypto-1.1.0-alpha06\res
com.focusflow.app-jetified-hilt-navigation-1.1.0-1 C:\Users\<USER>\.gradle\caches\8.12\transforms\0715f2ded0511da1e5dc510e67116e36\transformed\jetified-hilt-navigation-1.1.0\res
com.focusflow.app-jetified-hilt-work-1.1.0-2 C:\Users\<USER>\.gradle\caches\8.12\transforms\076c8c54142036a60ab1c5a2fb077ebf\transformed\jetified-hilt-work-1.1.0\res
com.focusflow.app-navigation-common-2.7.5-3 C:\Users\<USER>\.gradle\caches\8.12\transforms\0939b5539bd9819785f5853c9457da19\transformed\navigation-common-2.7.5\res
com.focusflow.app-jetified-startup-runtime-1.1.1-4 C:\Users\<USER>\.gradle\caches\8.12\transforms\0d10faef53a64c4cc4f5661f98403ffe\transformed\jetified-startup-runtime-1.1.1\res
com.focusflow.app-jetified-coil-base-2.5.0-5 C:\Users\<USER>\.gradle\caches\8.12\transforms\0d5221fa0308d516e8dafbc8d9244b53\transformed\jetified-coil-base-2.5.0\res
com.focusflow.app-biometric-1.1.0-6 C:\Users\<USER>\.gradle\caches\8.12\transforms\0e1dc919567705f737931e90f7aead7b\transformed\biometric-1.1.0\res
com.focusflow.app-room-runtime-2.6.1-7 C:\Users\<USER>\.gradle\caches\8.12\transforms\10a529dc4934703a517ccd294fe20872\transformed\room-runtime-2.6.1\res
com.focusflow.app-lifecycle-runtime-2.7.0-8 C:\Users\<USER>\.gradle\caches\8.12\transforms\1b3539420545eba470a6ac4d8c725246\transformed\lifecycle-runtime-2.7.0\res
com.focusflow.app-jetified-ui-geometry-release-9 C:\Users\<USER>\.gradle\caches\8.12\transforms\1d8c3fe9269e92d0a3e6b0cf1ae33961\transformed\jetified-ui-geometry-release\res
com.focusflow.app-jetified-material-release-10 C:\Users\<USER>\.gradle\caches\8.12\transforms\1d9c4f44b8060fc88db876a4dce6a477\transformed\jetified-material-release\res
com.focusflow.app-jetified-ui-release-11 C:\Users\<USER>\.gradle\caches\8.12\transforms\1f170a70d96fcd26f910cf5fef71c6ab\transformed\jetified-ui-release\res
com.focusflow.app-jetified-emoji2-1.4.0-12 C:\Users\<USER>\.gradle\caches\8.12\transforms\1fecd53a331ce0b963897f9da8c854aa\transformed\jetified-emoji2-1.4.0\res
com.focusflow.app-jetified-core-ktx-1.12.0-13 C:\Users\<USER>\.gradle\caches\8.12\transforms\203b30e82298f565fda56b7f4627386d\transformed\jetified-core-ktx-1.12.0\res
com.focusflow.app-jetified-savedstate-1.2.1-14 C:\Users\<USER>\.gradle\caches\8.12\transforms\21a6a6d8b8d5c5045da238ec9be68151\transformed\jetified-savedstate-1.2.1\res
com.focusflow.app-work-runtime-ktx-2.9.0-15 C:\Users\<USER>\.gradle\caches\8.12\transforms\251ce30526c21404d297545cb71e56a5\transformed\work-runtime-ktx-2.9.0\res
com.focusflow.app-core-runtime-2.2.0-16 C:\Users\<USER>\.gradle\caches\8.12\transforms\2a5f9de2b45882194175018fe0469237\transformed\core-runtime-2.2.0\res
com.focusflow.app-jetified-lifecycle-viewmodel-compose-2.7.0-17 C:\Users\<USER>\.gradle\caches\8.12\transforms\2ea480ea8c9c66f94db9e300ecfb2ed3\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\res
com.focusflow.app-jetified-ui-util-release-18 C:\Users\<USER>\.gradle\caches\8.12\transforms\319b71b6e6391419abc5b15b3b270788\transformed\jetified-ui-util-release\res
com.focusflow.app-jetified-lifecycle-runtime-compose-2.7.0-19 C:\Users\<USER>\.gradle\caches\8.12\transforms\39038db111d3a1fc6372d7eb1733a39c\transformed\jetified-lifecycle-runtime-compose-2.7.0\res
com.focusflow.app-jetified-annotation-experimental-1.3.0-20 C:\Users\<USER>\.gradle\caches\8.12\transforms\41588dac1ccb63a84af04a98d0131395\transformed\jetified-annotation-experimental-1.3.0\res
com.focusflow.app-jetified-lifecycle-runtime-ktx-2.7.0-21 C:\Users\<USER>\.gradle\caches\8.12\transforms\451d4e76371e0a6b66b2361bd9a42e8e\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.focusflow.app-lifecycle-livedata-2.7.0-22 C:\Users\<USER>\.gradle\caches\8.12\transforms\49c98ad063b66e655f27a8b6594ecae1\transformed\lifecycle-livedata-2.7.0\res
com.focusflow.app-jetified-lifecycle-service-2.7.0-23 C:\Users\<USER>\.gradle\caches\8.12\transforms\4a671a3f986dbe9efc78d72b6efa3e49\transformed\jetified-lifecycle-service-2.7.0\res
com.focusflow.app-jetified-savedstate-ktx-1.2.1-24 C:\Users\<USER>\.gradle\caches\8.12\transforms\514bc2681f80738c044823bc0126083d\transformed\jetified-savedstate-ktx-1.2.1\res
com.focusflow.app-jetified-datetime-0.9.0-25 C:\Users\<USER>\.gradle\caches\8.12\transforms\522eaa8dcceaba900a50e89ca4f51577\transformed\jetified-datetime-0.9.0\res
com.focusflow.app-jetified-ui-unit-release-26 C:\Users\<USER>\.gradle\caches\8.12\transforms\540f8de297042a7906895be5d56199a0\transformed\jetified-ui-unit-release\res
com.focusflow.app-jetified-foundation-release-27 C:\Users\<USER>\.gradle\caches\8.12\transforms\578305b89bd3ca9dee2bf1fe3893ba69\transformed\jetified-foundation-release\res
com.focusflow.app-navigation-runtime-2.7.5-28 C:\Users\<USER>\.gradle\caches\8.12\transforms\5e956e8c4b9ff7ae956b1bc7abb3c361\transformed\navigation-runtime-2.7.5\res
com.focusflow.app-jetified-lifecycle-livedata-core-ktx-2.7.0-29 C:\Users\<USER>\.gradle\caches\8.12\transforms\5ee4d1b7da8139aa2b7e24f3988a77e8\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.focusflow.app-navigation-common-ktx-2.7.5-30 C:\Users\<USER>\.gradle\caches\8.12\transforms\61645e0b75a4ada9ae5c27dd38056a97\transformed\navigation-common-ktx-2.7.5\res
com.focusflow.app-jetified-hilt-navigation-compose-1.1.0-31 C:\Users\<USER>\.gradle\caches\8.12\transforms\6d8c3d0d9894de3c4d59136e2d131b40\transformed\jetified-hilt-navigation-compose-1.1.0\res
com.focusflow.app-fragment-1.5.1-32 C:\Users\<USER>\.gradle\caches\8.12\transforms\6d9025e15716a4d8a0cfcec38b189854\transformed\fragment-1.5.1\res
com.focusflow.app-jetified-material-ripple-release-33 C:\Users\<USER>\.gradle\caches\8.12\transforms\761136709342550333e594f75a6496b3\transformed\jetified-material-ripple-release\res
com.focusflow.app-jetified-activity-1.8.2-34 C:\Users\<USER>\.gradle\caches\8.12\transforms\7c7c5a30567eb3e2d1c39db198a41f8c\transformed\jetified-activity-1.8.2\res
com.focusflow.app-jetified-ui-tooling-data-release-35 C:\Users\<USER>\.gradle\caches\8.12\transforms\807b45bbd8e841787a37598a409008c5\transformed\jetified-ui-tooling-data-release\res
com.focusflow.app-jetified-ui-text-release-36 C:\Users\<USER>\.gradle\caches\8.12\transforms\820fa46bc530437bf70aaa53ee0d33af\transformed\jetified-ui-text-release\res
com.focusflow.app-jetified-activity-compose-1.8.2-37 C:\Users\<USER>\.gradle\caches\8.12\transforms\83437aeff6196b298b04117d7acdaac7\transformed\jetified-activity-compose-1.8.2\res
com.focusflow.app-jetified-animation-core-release-38 C:\Users\<USER>\.gradle\caches\8.12\transforms\89a970527c344ee509b4d49c15e413fa\transformed\jetified-animation-core-release\res
com.focusflow.app-jetified-ui-tooling-preview-release-39 C:\Users\<USER>\.gradle\caches\8.12\transforms\8c3d8343e5aa681972c1538a36120bd6\transformed\jetified-ui-tooling-preview-release\res
com.focusflow.app-jetified-customview-poolingcontainer-1.0.0-40 C:\Users\<USER>\.gradle\caches\8.12\transforms\8cd39096ff5e90b2ee8900d321becb0c\transformed\jetified-customview-poolingcontainer-1.0.0\res
com.focusflow.app-jetified-navigation-compose-2.7.5-41 C:\Users\<USER>\.gradle\caches\8.12\transforms\8fbf1e882f089cc53014c7bd78a857b7\transformed\jetified-navigation-compose-2.7.5\res
com.focusflow.app-core-1.12.0-42 C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0\res
com.focusflow.app-jetified-room-ktx-2.6.1-43 C:\Users\<USER>\.gradle\caches\8.12\transforms\99626f5aff86c0f7ac0f9889669848a9\transformed\jetified-room-ktx-2.6.1\res
com.focusflow.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-44 C:\Users\<USER>\.gradle\caches\8.12\transforms\99f4f840bdcbf1643133703fa17830bc\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.focusflow.app-jetified-emoji2-views-helper-1.4.0-45 C:\Users\<USER>\.gradle\caches\8.12\transforms\9c9c38ae808df62f1ed29db049fc37f5\transformed\jetified-emoji2-views-helper-1.4.0\res
com.focusflow.app-jetified-lifecycle-process-2.7.0-46 C:\Users\<USER>\.gradle\caches\8.12\transforms\9fe1310f25ca312ed873f64d82bd0648\transformed\jetified-lifecycle-process-2.7.0\res
com.focusflow.app-jetified-ui-tooling-release-47 C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a2678a7cfe444c1610586a94808387\transformed\jetified-ui-tooling-release\res
com.focusflow.app-jetified-material-icons-core-release-48 C:\Users\<USER>\.gradle\caches\8.12\transforms\a1db5251133fd46e803afb5485ed86e3\transformed\jetified-material-icons-core-release\res
com.focusflow.app-jetified-ui-graphics-release-49 C:\Users\<USER>\.gradle\caches\8.12\transforms\a4db4a71831fe4752d07bd59b2538dc6\transformed\jetified-ui-graphics-release\res
com.focusflow.app-lifecycle-viewmodel-2.7.0-50 C:\Users\<USER>\.gradle\caches\8.12\transforms\a787f09ed2bafcdbe799a8b9000e6968\transformed\lifecycle-viewmodel-2.7.0\res
com.focusflow.app-jetified-appcompat-resources-1.6.1-51 C:\Users\<USER>\.gradle\caches\8.12\transforms\b11534671a794edd3e02caf79988e112\transformed\jetified-appcompat-resources-1.6.1\res
com.focusflow.app-appcompat-1.6.1-52 C:\Users\<USER>\.gradle\caches\8.12\transforms\b7575953e232ac886e0021593ed04f20\transformed\appcompat-1.6.1\res
com.focusflow.app-sqlite-2.4.0-53 C:\Users\<USER>\.gradle\caches\8.12\transforms\c10c278cfe66148a5f3cede494cd7071\transformed\sqlite-2.4.0\res
com.focusflow.app-jetified-foundation-layout-release-54 C:\Users\<USER>\.gradle\caches\8.12\transforms\c3a666876a1948e7171218a6769933a7\transformed\jetified-foundation-layout-release\res
com.focusflow.app-sqlite-framework-2.4.0-55 C:\Users\<USER>\.gradle\caches\8.12\transforms\c7ec984809a80c3ae349960313da4463\transformed\sqlite-framework-2.4.0\res
com.focusflow.app-jetified-ui-test-manifest-1.5.4-56 C:\Users\<USER>\.gradle\caches\8.12\transforms\cbcef85fc27e89f72d399aad76d2f59a\transformed\jetified-ui-test-manifest-1.5.4\res
com.focusflow.app-lifecycle-livedata-core-2.7.0-57 C:\Users\<USER>\.gradle\caches\8.12\transforms\cc5c1bf140349cf20e5d2264009975da\transformed\lifecycle-livedata-core-2.7.0\res
com.focusflow.app-jetified-runtime-release-58 C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2bb2f6242e20d4a17fc83490e591db\transformed\jetified-runtime-release\res
com.focusflow.app-jetified-lifecycle-viewmodel-ktx-2.7.0-59 C:\Users\<USER>\.gradle\caches\8.12\transforms\d57a180c9ec640813376e4de1defeced\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.focusflow.app-navigation-runtime-ktx-2.7.5-60 C:\Users\<USER>\.gradle\caches\8.12\transforms\d95a08e99875f3f7a49b5fc5f12afb36\transformed\navigation-runtime-ktx-2.7.5\res
com.focusflow.app-jetified-runtime-saveable-release-61 C:\Users\<USER>\.gradle\caches\8.12\transforms\d964e720205e6b8bad043f9899ef4633\transformed\jetified-runtime-saveable-release\res
com.focusflow.app-work-runtime-2.9.0-62 C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\res
com.focusflow.app-jetified-material-icons-extended-release-63 C:\Users\<USER>\.gradle\caches\8.12\transforms\ee44dfc1a226dd4bb89f6bd0c796857b\transformed\jetified-material-icons-extended-release\res
com.focusflow.app-jetified-activity-ktx-1.8.2-64 C:\Users\<USER>\.gradle\caches\8.12\transforms\ef90906aa87718a52589e1b5b65a5bd2\transformed\jetified-activity-ktx-1.8.2\res
com.focusflow.app-jetified-animation-release-65 C:\Users\<USER>\.gradle\caches\8.12\transforms\f29c2260e78a2407d7cb9466b240d8e9\transformed\jetified-animation-release\res
com.focusflow.app-jetified-profileinstaller-1.3.1-66 C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\res
com.focusflow.app-resValues-67 C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build\generated\res\resValues\debug
com.focusflow.app-packageDebugResources-68 C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.focusflow.app-packageDebugResources-69 C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.focusflow.app-debug-70 C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build\intermediates\merged_res\debug\mergeDebugResources
com.focusflow.app-debug-71 C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\debug\res
com.focusflow.app-main-72 C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res
