package com.focusflow.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0013\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B5\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0007\u0012\u0006\u0010\t\u001a\u00020\u0007\u0012\u0006\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\fJ\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u000bH\u00c6\u0003JE\u0010\u001d\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\u00072\b\b\u0002\u0010\n\u001a\u00020\u000bH\u00c6\u0001J\u0013\u0010\u001e\u001a\u00020\u001f2\b\u0010 \u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010!\u001a\u00020\u0005H\u00d6\u0001J\t\u0010\"\u001a\u00020#H\u00d6\u0001R\u0011\u0010\t\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u000eR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u000e\u00a8\u0006$"}, d2 = {"Lcom/focusflow/data/model/PayoffComparison;", "", "strategy", "Lcom/focusflow/ui/viewmodel/PayoffStrategy;", "totalMonths", "", "totalInterestPaid", "", "totalPayments", "monthlyPayment", "payoffDate", "Lkotlinx/datetime/LocalDate;", "(Lcom/focusflow/ui/viewmodel/PayoffStrategy;IDDDLkotlinx/datetime/LocalDate;)V", "getMonthlyPayment", "()D", "getPayoffDate", "()Lkotlinx/datetime/LocalDate;", "getStrategy", "()Lcom/focusflow/ui/viewmodel/PayoffStrategy;", "getTotalInterestPaid", "getTotalMonths", "()I", "getTotalPayments", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "toString", "", "app_debug"})
public final class PayoffComparison {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.ui.viewmodel.PayoffStrategy strategy = null;
    private final int totalMonths = 0;
    private final double totalInterestPaid = 0.0;
    private final double totalPayments = 0.0;
    private final double monthlyPayment = 0.0;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.datetime.LocalDate payoffDate = null;
    
    public PayoffComparison(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffStrategy strategy, int totalMonths, double totalInterestPaid, double totalPayments, double monthlyPayment, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate payoffDate) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.ui.viewmodel.PayoffStrategy getStrategy() {
        return null;
    }
    
    public final int getTotalMonths() {
        return 0;
    }
    
    public final double getTotalInterestPaid() {
        return 0.0;
    }
    
    public final double getTotalPayments() {
        return 0.0;
    }
    
    public final double getMonthlyPayment() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDate getPayoffDate() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.ui.viewmodel.PayoffStrategy component1() {
        return null;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final double component3() {
        return 0.0;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    public final double component5() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDate component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.PayoffComparison copy(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffStrategy strategy, int totalMonths, double totalInterestPaid, double totalPayments, double monthlyPayment, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate payoffDate) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}