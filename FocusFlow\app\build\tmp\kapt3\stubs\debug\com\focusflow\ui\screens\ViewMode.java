package com.focusflow.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0004\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004\u00a8\u0006\u0005"}, d2 = {"Lcom/focusflow/ui/screens/ViewMode;", "", "(Ljava/lang/String;I)V", "ENVELOPE", "LIST", "app_debug"})
public enum ViewMode {
    /*public static final*/ ENVELOPE /* = new ENVELOPE() */,
    /*public static final*/ LIST /* = new LIST() */;
    
    ViewMode() {
    }
    
    @org.jetbrains.annotations.NotNull
    public static kotlin.enums.EnumEntries<com.focusflow.ui.screens.ViewMode> getEntries() {
        return null;
    }
}