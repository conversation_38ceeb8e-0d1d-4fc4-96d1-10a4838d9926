package com.focusflow.service;

import android.content.Context;
import com.focusflow.data.database.FocusFlowDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PerformanceOptimizationService_Factory implements Factory<PerformanceOptimizationService> {
  private final Provider<Context> contextProvider;

  private final Provider<FocusFlowDatabase> databaseProvider;

  public PerformanceOptimizationService_Factory(Provider<Context> contextProvider,
      Provider<FocusFlowDatabase> databaseProvider) {
    this.contextProvider = contextProvider;
    this.databaseProvider = databaseProvider;
  }

  @Override
  public PerformanceOptimizationService get() {
    return newInstance(contextProvider.get(), databaseProvider.get());
  }

  public static PerformanceOptimizationService_Factory create(Provider<Context> contextProvider,
      Provider<FocusFlowDatabase> databaseProvider) {
    return new PerformanceOptimizationService_Factory(contextProvider, databaseProvider);
  }

  public static PerformanceOptimizationService newInstance(Context context,
      FocusFlowDatabase database) {
    return new PerformanceOptimizationService(context, database);
  }
}
