package com.focusflow.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\'\b\u0087\b\u0018\u00002\u00020\u0001Bi\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\n\u0012\u0006\u0010\u000b\u001a\u00020\b\u0012\u0006\u0010\f\u001a\u00020\b\u0012\u0006\u0010\r\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0010\u0012\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u0012\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\b\u00a2\u0006\u0002\u0010\u0014J\t\u0010(\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010)\u001a\u0004\u0018\u00010\u0012H\u00c6\u0003J\u0010\u0010*\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u001bJ\t\u0010+\u001a\u00020\u0005H\u00c6\u0003J\t\u0010,\u001a\u00020\u0005H\u00c6\u0003J\t\u0010-\u001a\u00020\bH\u00c6\u0003J\t\u0010.\u001a\u00020\nH\u00c6\u0003J\t\u0010/\u001a\u00020\bH\u00c6\u0003J\t\u00100\u001a\u00020\bH\u00c6\u0003J\t\u00101\u001a\u00020\u000eH\u00c6\u0003J\t\u00102\u001a\u00020\u0010H\u00c6\u0003J\u0080\u0001\u00103\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\b2\b\b\u0002\u0010\f\u001a\u00020\b2\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u00102\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u00122\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\bH\u00c6\u0001\u00a2\u0006\u0002\u00104J\u0013\u00105\u001a\u00020\u00102\b\u00106\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00107\u001a\u00020\nH\u00d6\u0001J\t\u00108\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u000f\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0019R\u0015\u0010\u0013\u001a\u0004\u0018\u00010\b\u00a2\u0006\n\n\u0002\u0010\u001c\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001eR\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u001eR\u0013\u0010\u0011\u001a\u0004\u0018\u00010\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010!R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010#R\u0011\u0010\u000b\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010#R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010&R\u0011\u0010\f\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010#\u00a8\u00069"}, d2 = {"Lcom/focusflow/data/model/PayoffPlan;", "", "id", "", "name", "", "strategy", "totalExtraPayment", "", "totalMonths", "", "totalInterestSaved", "totalPayments", "createdAt", "Lkotlinx/datetime/LocalDateTime;", "isActive", "", "targetPayoffDate", "Lkotlinx/datetime/LocalDate;", "monthlyBudgetAllocation", "(JLjava/lang/String;Ljava/lang/String;DIDDLkotlinx/datetime/LocalDateTime;ZLkotlinx/datetime/LocalDate;Ljava/lang/Double;)V", "getCreatedAt", "()Lkotlinx/datetime/LocalDateTime;", "getId", "()J", "()Z", "getMonthlyBudgetAllocation", "()Ljava/lang/Double;", "Ljava/lang/Double;", "getName", "()Ljava/lang/String;", "getStrategy", "getTargetPayoffDate", "()Lkotlinx/datetime/LocalDate;", "getTotalExtraPayment", "()D", "getTotalInterestSaved", "getTotalMonths", "()I", "getTotalPayments", "component1", "component10", "component11", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(JLjava/lang/String;Ljava/lang/String;DIDDLkotlinx/datetime/LocalDateTime;ZLkotlinx/datetime/LocalDate;Ljava/lang/Double;)Lcom/focusflow/data/model/PayoffPlan;", "equals", "other", "hashCode", "toString", "app_debug"})
@androidx.room.Entity(tableName = "payoff_plans")
public final class PayoffPlan {
    @androidx.room.PrimaryKey(autoGenerate = true)
    private final long id = 0L;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String name = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String strategy = null;
    private final double totalExtraPayment = 0.0;
    private final int totalMonths = 0;
    private final double totalInterestSaved = 0.0;
    private final double totalPayments = 0.0;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.datetime.LocalDateTime createdAt = null;
    private final boolean isActive = false;
    @org.jetbrains.annotations.Nullable
    private final kotlinx.datetime.LocalDate targetPayoffDate = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Double monthlyBudgetAllocation = null;
    
    public PayoffPlan(long id, @org.jetbrains.annotations.NotNull
    java.lang.String name, @org.jetbrains.annotations.NotNull
    java.lang.String strategy, double totalExtraPayment, int totalMonths, double totalInterestSaved, double totalPayments, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime createdAt, boolean isActive, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDate targetPayoffDate, @org.jetbrains.annotations.Nullable
    java.lang.Double monthlyBudgetAllocation) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getStrategy() {
        return null;
    }
    
    public final double getTotalExtraPayment() {
        return 0.0;
    }
    
    public final int getTotalMonths() {
        return 0;
    }
    
    public final double getTotalInterestSaved() {
        return 0.0;
    }
    
    public final double getTotalPayments() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDateTime getCreatedAt() {
        return null;
    }
    
    public final boolean isActive() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDate getTargetPayoffDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Double getMonthlyBudgetAllocation() {
        return null;
    }
    
    public final long component1() {
        return 0L;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDate component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Double component11() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component3() {
        return null;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    public final int component5() {
        return 0;
    }
    
    public final double component6() {
        return 0.0;
    }
    
    public final double component7() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDateTime component8() {
        return null;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.PayoffPlan copy(long id, @org.jetbrains.annotations.NotNull
    java.lang.String name, @org.jetbrains.annotations.NotNull
    java.lang.String strategy, double totalExtraPayment, int totalMonths, double totalInterestSaved, double totalPayments, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime createdAt, boolean isActive, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDate targetPayoffDate, @org.jetbrains.annotations.Nullable
    java.lang.Double monthlyBudgetAllocation) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}