package com.focusflow.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.focusflow.data.database.Converters;
import com.focusflow.data.model.BudgetAnalytics;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;
import kotlinx.datetime.LocalDateTime;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class BudgetAnalyticsDao_Impl implements BudgetAnalyticsDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<BudgetAnalytics> __insertionAdapterOfBudgetAnalytics;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<BudgetAnalytics> __deletionAdapterOfBudgetAnalytics;

  private final EntityDeletionOrUpdateAdapter<BudgetAnalytics> __updateAdapterOfBudgetAnalytics;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAnalyticsOlderThan;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAnalyticsForPeriod;

  public BudgetAnalyticsDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfBudgetAnalytics = new EntityInsertionAdapter<BudgetAnalytics>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `budget_analytics` (`id`,`categoryName`,`budgetPeriod`,`budgetYear`,`budgetMonth`,`budgetWeek`,`plannedAmount`,`actualSpent`,`variance`,`variancePercentage`,`trendDirection`,`averageTransactionSize`,`transactionCount`,`largestTransaction`,`smallestTransaction`,`mostFrequentMerchant`,`calculatedDate`,`daysInPeriod`,`projectedEndAmount`,`recommendedAdjustment`,`seasonalityFactor`,`isOutlierPeriod`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final BudgetAnalytics entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getCategoryName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getCategoryName());
        }
        if (entity.getBudgetPeriod() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getBudgetPeriod());
        }
        statement.bindLong(4, entity.getBudgetYear());
        if (entity.getBudgetMonth() == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, entity.getBudgetMonth());
        }
        if (entity.getBudgetWeek() == null) {
          statement.bindNull(6);
        } else {
          statement.bindLong(6, entity.getBudgetWeek());
        }
        statement.bindDouble(7, entity.getPlannedAmount());
        statement.bindDouble(8, entity.getActualSpent());
        statement.bindDouble(9, entity.getVariance());
        statement.bindDouble(10, entity.getVariancePercentage());
        if (entity.getTrendDirection() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getTrendDirection());
        }
        statement.bindDouble(12, entity.getAverageTransactionSize());
        statement.bindLong(13, entity.getTransactionCount());
        statement.bindDouble(14, entity.getLargestTransaction());
        statement.bindDouble(15, entity.getSmallestTransaction());
        if (entity.getMostFrequentMerchant() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getMostFrequentMerchant());
        }
        final String _tmp = __converters.fromLocalDateTime(entity.getCalculatedDate());
        if (_tmp == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, _tmp);
        }
        statement.bindLong(18, entity.getDaysInPeriod());
        if (entity.getProjectedEndAmount() == null) {
          statement.bindNull(19);
        } else {
          statement.bindDouble(19, entity.getProjectedEndAmount());
        }
        if (entity.getRecommendedAdjustment() == null) {
          statement.bindNull(20);
        } else {
          statement.bindDouble(20, entity.getRecommendedAdjustment());
        }
        statement.bindDouble(21, entity.getSeasonalityFactor());
        final int _tmp_1 = entity.isOutlierPeriod() ? 1 : 0;
        statement.bindLong(22, _tmp_1);
      }
    };
    this.__deletionAdapterOfBudgetAnalytics = new EntityDeletionOrUpdateAdapter<BudgetAnalytics>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `budget_analytics` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final BudgetAnalytics entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfBudgetAnalytics = new EntityDeletionOrUpdateAdapter<BudgetAnalytics>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `budget_analytics` SET `id` = ?,`categoryName` = ?,`budgetPeriod` = ?,`budgetYear` = ?,`budgetMonth` = ?,`budgetWeek` = ?,`plannedAmount` = ?,`actualSpent` = ?,`variance` = ?,`variancePercentage` = ?,`trendDirection` = ?,`averageTransactionSize` = ?,`transactionCount` = ?,`largestTransaction` = ?,`smallestTransaction` = ?,`mostFrequentMerchant` = ?,`calculatedDate` = ?,`daysInPeriod` = ?,`projectedEndAmount` = ?,`recommendedAdjustment` = ?,`seasonalityFactor` = ?,`isOutlierPeriod` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final BudgetAnalytics entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getCategoryName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getCategoryName());
        }
        if (entity.getBudgetPeriod() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getBudgetPeriod());
        }
        statement.bindLong(4, entity.getBudgetYear());
        if (entity.getBudgetMonth() == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, entity.getBudgetMonth());
        }
        if (entity.getBudgetWeek() == null) {
          statement.bindNull(6);
        } else {
          statement.bindLong(6, entity.getBudgetWeek());
        }
        statement.bindDouble(7, entity.getPlannedAmount());
        statement.bindDouble(8, entity.getActualSpent());
        statement.bindDouble(9, entity.getVariance());
        statement.bindDouble(10, entity.getVariancePercentage());
        if (entity.getTrendDirection() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getTrendDirection());
        }
        statement.bindDouble(12, entity.getAverageTransactionSize());
        statement.bindLong(13, entity.getTransactionCount());
        statement.bindDouble(14, entity.getLargestTransaction());
        statement.bindDouble(15, entity.getSmallestTransaction());
        if (entity.getMostFrequentMerchant() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getMostFrequentMerchant());
        }
        final String _tmp = __converters.fromLocalDateTime(entity.getCalculatedDate());
        if (_tmp == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, _tmp);
        }
        statement.bindLong(18, entity.getDaysInPeriod());
        if (entity.getProjectedEndAmount() == null) {
          statement.bindNull(19);
        } else {
          statement.bindDouble(19, entity.getProjectedEndAmount());
        }
        if (entity.getRecommendedAdjustment() == null) {
          statement.bindNull(20);
        } else {
          statement.bindDouble(20, entity.getRecommendedAdjustment());
        }
        statement.bindDouble(21, entity.getSeasonalityFactor());
        final int _tmp_1 = entity.isOutlierPeriod() ? 1 : 0;
        statement.bindLong(22, _tmp_1);
        statement.bindLong(23, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteAnalyticsOlderThan = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM budget_analytics WHERE calculatedDate < ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAnalyticsForPeriod = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM budget_analytics WHERE categoryName = ? AND budgetPeriod = ? AND budgetYear = ? AND budgetMonth = ? AND budgetWeek = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertAnalytics(final BudgetAnalytics analytics,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfBudgetAnalytics.insertAndReturnId(analytics);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAnalytics(final BudgetAnalytics analytics,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfBudgetAnalytics.handle(analytics);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateAnalytics(final BudgetAnalytics analytics,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfBudgetAnalytics.handle(analytics);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAnalyticsOlderThan(final LocalDateTime cutoffDate,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAnalyticsOlderThan.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromLocalDateTime(cutoffDate);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAnalyticsOlderThan.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAnalyticsForPeriod(final String categoryName, final String period,
      final int year, final Integer month, final Integer week,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAnalyticsForPeriod.acquire();
        int _argIndex = 1;
        if (categoryName == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, categoryName);
        }
        _argIndex = 2;
        if (period == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, period);
        }
        _argIndex = 3;
        _stmt.bindLong(_argIndex, year);
        _argIndex = 4;
        if (month == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindLong(_argIndex, month);
        }
        _argIndex = 5;
        if (week == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindLong(_argIndex, week);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAnalyticsForPeriod.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<BudgetAnalytics>> getAllAnalytics() {
    final String _sql = "SELECT * FROM budget_analytics ORDER BY calculatedDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"budget_analytics"}, new Callable<List<BudgetAnalytics>>() {
      @Override
      @NonNull
      public List<BudgetAnalytics> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryName");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetPeriod");
          final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetYear");
          final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetMonth");
          final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetWeek");
          final int _cursorIndexOfPlannedAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "plannedAmount");
          final int _cursorIndexOfActualSpent = CursorUtil.getColumnIndexOrThrow(_cursor, "actualSpent");
          final int _cursorIndexOfVariance = CursorUtil.getColumnIndexOrThrow(_cursor, "variance");
          final int _cursorIndexOfVariancePercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "variancePercentage");
          final int _cursorIndexOfTrendDirection = CursorUtil.getColumnIndexOrThrow(_cursor, "trendDirection");
          final int _cursorIndexOfAverageTransactionSize = CursorUtil.getColumnIndexOrThrow(_cursor, "averageTransactionSize");
          final int _cursorIndexOfTransactionCount = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionCount");
          final int _cursorIndexOfLargestTransaction = CursorUtil.getColumnIndexOrThrow(_cursor, "largestTransaction");
          final int _cursorIndexOfSmallestTransaction = CursorUtil.getColumnIndexOrThrow(_cursor, "smallestTransaction");
          final int _cursorIndexOfMostFrequentMerchant = CursorUtil.getColumnIndexOrThrow(_cursor, "mostFrequentMerchant");
          final int _cursorIndexOfCalculatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "calculatedDate");
          final int _cursorIndexOfDaysInPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "daysInPeriod");
          final int _cursorIndexOfProjectedEndAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "projectedEndAmount");
          final int _cursorIndexOfRecommendedAdjustment = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendedAdjustment");
          final int _cursorIndexOfSeasonalityFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalityFactor");
          final int _cursorIndexOfIsOutlierPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "isOutlierPeriod");
          final List<BudgetAnalytics> _result = new ArrayList<BudgetAnalytics>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BudgetAnalytics _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCategoryName;
            if (_cursor.isNull(_cursorIndexOfCategoryName)) {
              _tmpCategoryName = null;
            } else {
              _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
            }
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            final int _tmpBudgetYear;
            _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
            final Integer _tmpBudgetMonth;
            if (_cursor.isNull(_cursorIndexOfBudgetMonth)) {
              _tmpBudgetMonth = null;
            } else {
              _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
            }
            final Integer _tmpBudgetWeek;
            if (_cursor.isNull(_cursorIndexOfBudgetWeek)) {
              _tmpBudgetWeek = null;
            } else {
              _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
            }
            final double _tmpPlannedAmount;
            _tmpPlannedAmount = _cursor.getDouble(_cursorIndexOfPlannedAmount);
            final double _tmpActualSpent;
            _tmpActualSpent = _cursor.getDouble(_cursorIndexOfActualSpent);
            final double _tmpVariance;
            _tmpVariance = _cursor.getDouble(_cursorIndexOfVariance);
            final double _tmpVariancePercentage;
            _tmpVariancePercentage = _cursor.getDouble(_cursorIndexOfVariancePercentage);
            final String _tmpTrendDirection;
            if (_cursor.isNull(_cursorIndexOfTrendDirection)) {
              _tmpTrendDirection = null;
            } else {
              _tmpTrendDirection = _cursor.getString(_cursorIndexOfTrendDirection);
            }
            final double _tmpAverageTransactionSize;
            _tmpAverageTransactionSize = _cursor.getDouble(_cursorIndexOfAverageTransactionSize);
            final int _tmpTransactionCount;
            _tmpTransactionCount = _cursor.getInt(_cursorIndexOfTransactionCount);
            final double _tmpLargestTransaction;
            _tmpLargestTransaction = _cursor.getDouble(_cursorIndexOfLargestTransaction);
            final double _tmpSmallestTransaction;
            _tmpSmallestTransaction = _cursor.getDouble(_cursorIndexOfSmallestTransaction);
            final String _tmpMostFrequentMerchant;
            if (_cursor.isNull(_cursorIndexOfMostFrequentMerchant)) {
              _tmpMostFrequentMerchant = null;
            } else {
              _tmpMostFrequentMerchant = _cursor.getString(_cursorIndexOfMostFrequentMerchant);
            }
            final LocalDateTime _tmpCalculatedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCalculatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCalculatedDate);
            }
            _tmpCalculatedDate = __converters.toLocalDateTime(_tmp);
            final int _tmpDaysInPeriod;
            _tmpDaysInPeriod = _cursor.getInt(_cursorIndexOfDaysInPeriod);
            final Double _tmpProjectedEndAmount;
            if (_cursor.isNull(_cursorIndexOfProjectedEndAmount)) {
              _tmpProjectedEndAmount = null;
            } else {
              _tmpProjectedEndAmount = _cursor.getDouble(_cursorIndexOfProjectedEndAmount);
            }
            final Double _tmpRecommendedAdjustment;
            if (_cursor.isNull(_cursorIndexOfRecommendedAdjustment)) {
              _tmpRecommendedAdjustment = null;
            } else {
              _tmpRecommendedAdjustment = _cursor.getDouble(_cursorIndexOfRecommendedAdjustment);
            }
            final double _tmpSeasonalityFactor;
            _tmpSeasonalityFactor = _cursor.getDouble(_cursorIndexOfSeasonalityFactor);
            final boolean _tmpIsOutlierPeriod;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsOutlierPeriod);
            _tmpIsOutlierPeriod = _tmp_1 != 0;
            _item = new BudgetAnalytics(_tmpId,_tmpCategoryName,_tmpBudgetPeriod,_tmpBudgetYear,_tmpBudgetMonth,_tmpBudgetWeek,_tmpPlannedAmount,_tmpActualSpent,_tmpVariance,_tmpVariancePercentage,_tmpTrendDirection,_tmpAverageTransactionSize,_tmpTransactionCount,_tmpLargestTransaction,_tmpSmallestTransaction,_tmpMostFrequentMerchant,_tmpCalculatedDate,_tmpDaysInPeriod,_tmpProjectedEndAmount,_tmpRecommendedAdjustment,_tmpSeasonalityFactor,_tmpIsOutlierPeriod);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<BudgetAnalytics>> getAnalyticsByCategory(final String categoryName) {
    final String _sql = "SELECT * FROM budget_analytics WHERE categoryName = ? ORDER BY calculatedDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (categoryName == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, categoryName);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"budget_analytics"}, new Callable<List<BudgetAnalytics>>() {
      @Override
      @NonNull
      public List<BudgetAnalytics> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryName");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetPeriod");
          final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetYear");
          final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetMonth");
          final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetWeek");
          final int _cursorIndexOfPlannedAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "plannedAmount");
          final int _cursorIndexOfActualSpent = CursorUtil.getColumnIndexOrThrow(_cursor, "actualSpent");
          final int _cursorIndexOfVariance = CursorUtil.getColumnIndexOrThrow(_cursor, "variance");
          final int _cursorIndexOfVariancePercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "variancePercentage");
          final int _cursorIndexOfTrendDirection = CursorUtil.getColumnIndexOrThrow(_cursor, "trendDirection");
          final int _cursorIndexOfAverageTransactionSize = CursorUtil.getColumnIndexOrThrow(_cursor, "averageTransactionSize");
          final int _cursorIndexOfTransactionCount = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionCount");
          final int _cursorIndexOfLargestTransaction = CursorUtil.getColumnIndexOrThrow(_cursor, "largestTransaction");
          final int _cursorIndexOfSmallestTransaction = CursorUtil.getColumnIndexOrThrow(_cursor, "smallestTransaction");
          final int _cursorIndexOfMostFrequentMerchant = CursorUtil.getColumnIndexOrThrow(_cursor, "mostFrequentMerchant");
          final int _cursorIndexOfCalculatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "calculatedDate");
          final int _cursorIndexOfDaysInPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "daysInPeriod");
          final int _cursorIndexOfProjectedEndAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "projectedEndAmount");
          final int _cursorIndexOfRecommendedAdjustment = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendedAdjustment");
          final int _cursorIndexOfSeasonalityFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalityFactor");
          final int _cursorIndexOfIsOutlierPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "isOutlierPeriod");
          final List<BudgetAnalytics> _result = new ArrayList<BudgetAnalytics>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BudgetAnalytics _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCategoryName;
            if (_cursor.isNull(_cursorIndexOfCategoryName)) {
              _tmpCategoryName = null;
            } else {
              _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
            }
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            final int _tmpBudgetYear;
            _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
            final Integer _tmpBudgetMonth;
            if (_cursor.isNull(_cursorIndexOfBudgetMonth)) {
              _tmpBudgetMonth = null;
            } else {
              _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
            }
            final Integer _tmpBudgetWeek;
            if (_cursor.isNull(_cursorIndexOfBudgetWeek)) {
              _tmpBudgetWeek = null;
            } else {
              _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
            }
            final double _tmpPlannedAmount;
            _tmpPlannedAmount = _cursor.getDouble(_cursorIndexOfPlannedAmount);
            final double _tmpActualSpent;
            _tmpActualSpent = _cursor.getDouble(_cursorIndexOfActualSpent);
            final double _tmpVariance;
            _tmpVariance = _cursor.getDouble(_cursorIndexOfVariance);
            final double _tmpVariancePercentage;
            _tmpVariancePercentage = _cursor.getDouble(_cursorIndexOfVariancePercentage);
            final String _tmpTrendDirection;
            if (_cursor.isNull(_cursorIndexOfTrendDirection)) {
              _tmpTrendDirection = null;
            } else {
              _tmpTrendDirection = _cursor.getString(_cursorIndexOfTrendDirection);
            }
            final double _tmpAverageTransactionSize;
            _tmpAverageTransactionSize = _cursor.getDouble(_cursorIndexOfAverageTransactionSize);
            final int _tmpTransactionCount;
            _tmpTransactionCount = _cursor.getInt(_cursorIndexOfTransactionCount);
            final double _tmpLargestTransaction;
            _tmpLargestTransaction = _cursor.getDouble(_cursorIndexOfLargestTransaction);
            final double _tmpSmallestTransaction;
            _tmpSmallestTransaction = _cursor.getDouble(_cursorIndexOfSmallestTransaction);
            final String _tmpMostFrequentMerchant;
            if (_cursor.isNull(_cursorIndexOfMostFrequentMerchant)) {
              _tmpMostFrequentMerchant = null;
            } else {
              _tmpMostFrequentMerchant = _cursor.getString(_cursorIndexOfMostFrequentMerchant);
            }
            final LocalDateTime _tmpCalculatedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCalculatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCalculatedDate);
            }
            _tmpCalculatedDate = __converters.toLocalDateTime(_tmp);
            final int _tmpDaysInPeriod;
            _tmpDaysInPeriod = _cursor.getInt(_cursorIndexOfDaysInPeriod);
            final Double _tmpProjectedEndAmount;
            if (_cursor.isNull(_cursorIndexOfProjectedEndAmount)) {
              _tmpProjectedEndAmount = null;
            } else {
              _tmpProjectedEndAmount = _cursor.getDouble(_cursorIndexOfProjectedEndAmount);
            }
            final Double _tmpRecommendedAdjustment;
            if (_cursor.isNull(_cursorIndexOfRecommendedAdjustment)) {
              _tmpRecommendedAdjustment = null;
            } else {
              _tmpRecommendedAdjustment = _cursor.getDouble(_cursorIndexOfRecommendedAdjustment);
            }
            final double _tmpSeasonalityFactor;
            _tmpSeasonalityFactor = _cursor.getDouble(_cursorIndexOfSeasonalityFactor);
            final boolean _tmpIsOutlierPeriod;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsOutlierPeriod);
            _tmpIsOutlierPeriod = _tmp_1 != 0;
            _item = new BudgetAnalytics(_tmpId,_tmpCategoryName,_tmpBudgetPeriod,_tmpBudgetYear,_tmpBudgetMonth,_tmpBudgetWeek,_tmpPlannedAmount,_tmpActualSpent,_tmpVariance,_tmpVariancePercentage,_tmpTrendDirection,_tmpAverageTransactionSize,_tmpTransactionCount,_tmpLargestTransaction,_tmpSmallestTransaction,_tmpMostFrequentMerchant,_tmpCalculatedDate,_tmpDaysInPeriod,_tmpProjectedEndAmount,_tmpRecommendedAdjustment,_tmpSeasonalityFactor,_tmpIsOutlierPeriod);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<BudgetAnalytics>> getAnalyticsForPeriod(final String period, final int year,
      final Integer month) {
    final String _sql = "SELECT * FROM budget_analytics WHERE budgetPeriod = ? AND budgetYear = ? AND budgetMonth = ? ORDER BY categoryName";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, year);
    _argIndex = 3;
    if (month == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, month);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"budget_analytics"}, new Callable<List<BudgetAnalytics>>() {
      @Override
      @NonNull
      public List<BudgetAnalytics> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryName");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetPeriod");
          final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetYear");
          final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetMonth");
          final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetWeek");
          final int _cursorIndexOfPlannedAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "plannedAmount");
          final int _cursorIndexOfActualSpent = CursorUtil.getColumnIndexOrThrow(_cursor, "actualSpent");
          final int _cursorIndexOfVariance = CursorUtil.getColumnIndexOrThrow(_cursor, "variance");
          final int _cursorIndexOfVariancePercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "variancePercentage");
          final int _cursorIndexOfTrendDirection = CursorUtil.getColumnIndexOrThrow(_cursor, "trendDirection");
          final int _cursorIndexOfAverageTransactionSize = CursorUtil.getColumnIndexOrThrow(_cursor, "averageTransactionSize");
          final int _cursorIndexOfTransactionCount = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionCount");
          final int _cursorIndexOfLargestTransaction = CursorUtil.getColumnIndexOrThrow(_cursor, "largestTransaction");
          final int _cursorIndexOfSmallestTransaction = CursorUtil.getColumnIndexOrThrow(_cursor, "smallestTransaction");
          final int _cursorIndexOfMostFrequentMerchant = CursorUtil.getColumnIndexOrThrow(_cursor, "mostFrequentMerchant");
          final int _cursorIndexOfCalculatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "calculatedDate");
          final int _cursorIndexOfDaysInPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "daysInPeriod");
          final int _cursorIndexOfProjectedEndAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "projectedEndAmount");
          final int _cursorIndexOfRecommendedAdjustment = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendedAdjustment");
          final int _cursorIndexOfSeasonalityFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalityFactor");
          final int _cursorIndexOfIsOutlierPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "isOutlierPeriod");
          final List<BudgetAnalytics> _result = new ArrayList<BudgetAnalytics>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BudgetAnalytics _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCategoryName;
            if (_cursor.isNull(_cursorIndexOfCategoryName)) {
              _tmpCategoryName = null;
            } else {
              _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
            }
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            final int _tmpBudgetYear;
            _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
            final Integer _tmpBudgetMonth;
            if (_cursor.isNull(_cursorIndexOfBudgetMonth)) {
              _tmpBudgetMonth = null;
            } else {
              _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
            }
            final Integer _tmpBudgetWeek;
            if (_cursor.isNull(_cursorIndexOfBudgetWeek)) {
              _tmpBudgetWeek = null;
            } else {
              _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
            }
            final double _tmpPlannedAmount;
            _tmpPlannedAmount = _cursor.getDouble(_cursorIndexOfPlannedAmount);
            final double _tmpActualSpent;
            _tmpActualSpent = _cursor.getDouble(_cursorIndexOfActualSpent);
            final double _tmpVariance;
            _tmpVariance = _cursor.getDouble(_cursorIndexOfVariance);
            final double _tmpVariancePercentage;
            _tmpVariancePercentage = _cursor.getDouble(_cursorIndexOfVariancePercentage);
            final String _tmpTrendDirection;
            if (_cursor.isNull(_cursorIndexOfTrendDirection)) {
              _tmpTrendDirection = null;
            } else {
              _tmpTrendDirection = _cursor.getString(_cursorIndexOfTrendDirection);
            }
            final double _tmpAverageTransactionSize;
            _tmpAverageTransactionSize = _cursor.getDouble(_cursorIndexOfAverageTransactionSize);
            final int _tmpTransactionCount;
            _tmpTransactionCount = _cursor.getInt(_cursorIndexOfTransactionCount);
            final double _tmpLargestTransaction;
            _tmpLargestTransaction = _cursor.getDouble(_cursorIndexOfLargestTransaction);
            final double _tmpSmallestTransaction;
            _tmpSmallestTransaction = _cursor.getDouble(_cursorIndexOfSmallestTransaction);
            final String _tmpMostFrequentMerchant;
            if (_cursor.isNull(_cursorIndexOfMostFrequentMerchant)) {
              _tmpMostFrequentMerchant = null;
            } else {
              _tmpMostFrequentMerchant = _cursor.getString(_cursorIndexOfMostFrequentMerchant);
            }
            final LocalDateTime _tmpCalculatedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCalculatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCalculatedDate);
            }
            _tmpCalculatedDate = __converters.toLocalDateTime(_tmp);
            final int _tmpDaysInPeriod;
            _tmpDaysInPeriod = _cursor.getInt(_cursorIndexOfDaysInPeriod);
            final Double _tmpProjectedEndAmount;
            if (_cursor.isNull(_cursorIndexOfProjectedEndAmount)) {
              _tmpProjectedEndAmount = null;
            } else {
              _tmpProjectedEndAmount = _cursor.getDouble(_cursorIndexOfProjectedEndAmount);
            }
            final Double _tmpRecommendedAdjustment;
            if (_cursor.isNull(_cursorIndexOfRecommendedAdjustment)) {
              _tmpRecommendedAdjustment = null;
            } else {
              _tmpRecommendedAdjustment = _cursor.getDouble(_cursorIndexOfRecommendedAdjustment);
            }
            final double _tmpSeasonalityFactor;
            _tmpSeasonalityFactor = _cursor.getDouble(_cursorIndexOfSeasonalityFactor);
            final boolean _tmpIsOutlierPeriod;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsOutlierPeriod);
            _tmpIsOutlierPeriod = _tmp_1 != 0;
            _item = new BudgetAnalytics(_tmpId,_tmpCategoryName,_tmpBudgetPeriod,_tmpBudgetYear,_tmpBudgetMonth,_tmpBudgetWeek,_tmpPlannedAmount,_tmpActualSpent,_tmpVariance,_tmpVariancePercentage,_tmpTrendDirection,_tmpAverageTransactionSize,_tmpTransactionCount,_tmpLargestTransaction,_tmpSmallestTransaction,_tmpMostFrequentMerchant,_tmpCalculatedDate,_tmpDaysInPeriod,_tmpProjectedEndAmount,_tmpRecommendedAdjustment,_tmpSeasonalityFactor,_tmpIsOutlierPeriod);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getAnalyticsForCategoryAndPeriod(final String categoryName, final String period,
      final int year, final Integer month, final Integer week,
      final Continuation<? super BudgetAnalytics> $completion) {
    final String _sql = "SELECT * FROM budget_analytics WHERE categoryName = ? AND budgetPeriod = ? AND budgetYear = ? AND budgetMonth = ? AND budgetWeek = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 5);
    int _argIndex = 1;
    if (categoryName == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, categoryName);
    }
    _argIndex = 2;
    if (period == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, period);
    }
    _argIndex = 3;
    _statement.bindLong(_argIndex, year);
    _argIndex = 4;
    if (month == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, month);
    }
    _argIndex = 5;
    if (week == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, week);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<BudgetAnalytics>() {
      @Override
      @Nullable
      public BudgetAnalytics call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryName");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetPeriod");
          final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetYear");
          final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetMonth");
          final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetWeek");
          final int _cursorIndexOfPlannedAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "plannedAmount");
          final int _cursorIndexOfActualSpent = CursorUtil.getColumnIndexOrThrow(_cursor, "actualSpent");
          final int _cursorIndexOfVariance = CursorUtil.getColumnIndexOrThrow(_cursor, "variance");
          final int _cursorIndexOfVariancePercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "variancePercentage");
          final int _cursorIndexOfTrendDirection = CursorUtil.getColumnIndexOrThrow(_cursor, "trendDirection");
          final int _cursorIndexOfAverageTransactionSize = CursorUtil.getColumnIndexOrThrow(_cursor, "averageTransactionSize");
          final int _cursorIndexOfTransactionCount = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionCount");
          final int _cursorIndexOfLargestTransaction = CursorUtil.getColumnIndexOrThrow(_cursor, "largestTransaction");
          final int _cursorIndexOfSmallestTransaction = CursorUtil.getColumnIndexOrThrow(_cursor, "smallestTransaction");
          final int _cursorIndexOfMostFrequentMerchant = CursorUtil.getColumnIndexOrThrow(_cursor, "mostFrequentMerchant");
          final int _cursorIndexOfCalculatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "calculatedDate");
          final int _cursorIndexOfDaysInPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "daysInPeriod");
          final int _cursorIndexOfProjectedEndAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "projectedEndAmount");
          final int _cursorIndexOfRecommendedAdjustment = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendedAdjustment");
          final int _cursorIndexOfSeasonalityFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalityFactor");
          final int _cursorIndexOfIsOutlierPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "isOutlierPeriod");
          final BudgetAnalytics _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCategoryName;
            if (_cursor.isNull(_cursorIndexOfCategoryName)) {
              _tmpCategoryName = null;
            } else {
              _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
            }
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            final int _tmpBudgetYear;
            _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
            final Integer _tmpBudgetMonth;
            if (_cursor.isNull(_cursorIndexOfBudgetMonth)) {
              _tmpBudgetMonth = null;
            } else {
              _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
            }
            final Integer _tmpBudgetWeek;
            if (_cursor.isNull(_cursorIndexOfBudgetWeek)) {
              _tmpBudgetWeek = null;
            } else {
              _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
            }
            final double _tmpPlannedAmount;
            _tmpPlannedAmount = _cursor.getDouble(_cursorIndexOfPlannedAmount);
            final double _tmpActualSpent;
            _tmpActualSpent = _cursor.getDouble(_cursorIndexOfActualSpent);
            final double _tmpVariance;
            _tmpVariance = _cursor.getDouble(_cursorIndexOfVariance);
            final double _tmpVariancePercentage;
            _tmpVariancePercentage = _cursor.getDouble(_cursorIndexOfVariancePercentage);
            final String _tmpTrendDirection;
            if (_cursor.isNull(_cursorIndexOfTrendDirection)) {
              _tmpTrendDirection = null;
            } else {
              _tmpTrendDirection = _cursor.getString(_cursorIndexOfTrendDirection);
            }
            final double _tmpAverageTransactionSize;
            _tmpAverageTransactionSize = _cursor.getDouble(_cursorIndexOfAverageTransactionSize);
            final int _tmpTransactionCount;
            _tmpTransactionCount = _cursor.getInt(_cursorIndexOfTransactionCount);
            final double _tmpLargestTransaction;
            _tmpLargestTransaction = _cursor.getDouble(_cursorIndexOfLargestTransaction);
            final double _tmpSmallestTransaction;
            _tmpSmallestTransaction = _cursor.getDouble(_cursorIndexOfSmallestTransaction);
            final String _tmpMostFrequentMerchant;
            if (_cursor.isNull(_cursorIndexOfMostFrequentMerchant)) {
              _tmpMostFrequentMerchant = null;
            } else {
              _tmpMostFrequentMerchant = _cursor.getString(_cursorIndexOfMostFrequentMerchant);
            }
            final LocalDateTime _tmpCalculatedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCalculatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCalculatedDate);
            }
            _tmpCalculatedDate = __converters.toLocalDateTime(_tmp);
            final int _tmpDaysInPeriod;
            _tmpDaysInPeriod = _cursor.getInt(_cursorIndexOfDaysInPeriod);
            final Double _tmpProjectedEndAmount;
            if (_cursor.isNull(_cursorIndexOfProjectedEndAmount)) {
              _tmpProjectedEndAmount = null;
            } else {
              _tmpProjectedEndAmount = _cursor.getDouble(_cursorIndexOfProjectedEndAmount);
            }
            final Double _tmpRecommendedAdjustment;
            if (_cursor.isNull(_cursorIndexOfRecommendedAdjustment)) {
              _tmpRecommendedAdjustment = null;
            } else {
              _tmpRecommendedAdjustment = _cursor.getDouble(_cursorIndexOfRecommendedAdjustment);
            }
            final double _tmpSeasonalityFactor;
            _tmpSeasonalityFactor = _cursor.getDouble(_cursorIndexOfSeasonalityFactor);
            final boolean _tmpIsOutlierPeriod;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsOutlierPeriod);
            _tmpIsOutlierPeriod = _tmp_1 != 0;
            _result = new BudgetAnalytics(_tmpId,_tmpCategoryName,_tmpBudgetPeriod,_tmpBudgetYear,_tmpBudgetMonth,_tmpBudgetWeek,_tmpPlannedAmount,_tmpActualSpent,_tmpVariance,_tmpVariancePercentage,_tmpTrendDirection,_tmpAverageTransactionSize,_tmpTransactionCount,_tmpLargestTransaction,_tmpSmallestTransaction,_tmpMostFrequentMerchant,_tmpCalculatedDate,_tmpDaysInPeriod,_tmpProjectedEndAmount,_tmpRecommendedAdjustment,_tmpSeasonalityFactor,_tmpIsOutlierPeriod);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<BudgetAnalytics>> getHighVarianceAnalytics(final double threshold) {
    final String _sql = "SELECT * FROM budget_analytics WHERE variancePercentage > ? ORDER BY variancePercentage DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindDouble(_argIndex, threshold);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"budget_analytics"}, new Callable<List<BudgetAnalytics>>() {
      @Override
      @NonNull
      public List<BudgetAnalytics> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryName");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetPeriod");
          final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetYear");
          final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetMonth");
          final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetWeek");
          final int _cursorIndexOfPlannedAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "plannedAmount");
          final int _cursorIndexOfActualSpent = CursorUtil.getColumnIndexOrThrow(_cursor, "actualSpent");
          final int _cursorIndexOfVariance = CursorUtil.getColumnIndexOrThrow(_cursor, "variance");
          final int _cursorIndexOfVariancePercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "variancePercentage");
          final int _cursorIndexOfTrendDirection = CursorUtil.getColumnIndexOrThrow(_cursor, "trendDirection");
          final int _cursorIndexOfAverageTransactionSize = CursorUtil.getColumnIndexOrThrow(_cursor, "averageTransactionSize");
          final int _cursorIndexOfTransactionCount = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionCount");
          final int _cursorIndexOfLargestTransaction = CursorUtil.getColumnIndexOrThrow(_cursor, "largestTransaction");
          final int _cursorIndexOfSmallestTransaction = CursorUtil.getColumnIndexOrThrow(_cursor, "smallestTransaction");
          final int _cursorIndexOfMostFrequentMerchant = CursorUtil.getColumnIndexOrThrow(_cursor, "mostFrequentMerchant");
          final int _cursorIndexOfCalculatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "calculatedDate");
          final int _cursorIndexOfDaysInPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "daysInPeriod");
          final int _cursorIndexOfProjectedEndAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "projectedEndAmount");
          final int _cursorIndexOfRecommendedAdjustment = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendedAdjustment");
          final int _cursorIndexOfSeasonalityFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalityFactor");
          final int _cursorIndexOfIsOutlierPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "isOutlierPeriod");
          final List<BudgetAnalytics> _result = new ArrayList<BudgetAnalytics>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BudgetAnalytics _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCategoryName;
            if (_cursor.isNull(_cursorIndexOfCategoryName)) {
              _tmpCategoryName = null;
            } else {
              _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
            }
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            final int _tmpBudgetYear;
            _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
            final Integer _tmpBudgetMonth;
            if (_cursor.isNull(_cursorIndexOfBudgetMonth)) {
              _tmpBudgetMonth = null;
            } else {
              _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
            }
            final Integer _tmpBudgetWeek;
            if (_cursor.isNull(_cursorIndexOfBudgetWeek)) {
              _tmpBudgetWeek = null;
            } else {
              _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
            }
            final double _tmpPlannedAmount;
            _tmpPlannedAmount = _cursor.getDouble(_cursorIndexOfPlannedAmount);
            final double _tmpActualSpent;
            _tmpActualSpent = _cursor.getDouble(_cursorIndexOfActualSpent);
            final double _tmpVariance;
            _tmpVariance = _cursor.getDouble(_cursorIndexOfVariance);
            final double _tmpVariancePercentage;
            _tmpVariancePercentage = _cursor.getDouble(_cursorIndexOfVariancePercentage);
            final String _tmpTrendDirection;
            if (_cursor.isNull(_cursorIndexOfTrendDirection)) {
              _tmpTrendDirection = null;
            } else {
              _tmpTrendDirection = _cursor.getString(_cursorIndexOfTrendDirection);
            }
            final double _tmpAverageTransactionSize;
            _tmpAverageTransactionSize = _cursor.getDouble(_cursorIndexOfAverageTransactionSize);
            final int _tmpTransactionCount;
            _tmpTransactionCount = _cursor.getInt(_cursorIndexOfTransactionCount);
            final double _tmpLargestTransaction;
            _tmpLargestTransaction = _cursor.getDouble(_cursorIndexOfLargestTransaction);
            final double _tmpSmallestTransaction;
            _tmpSmallestTransaction = _cursor.getDouble(_cursorIndexOfSmallestTransaction);
            final String _tmpMostFrequentMerchant;
            if (_cursor.isNull(_cursorIndexOfMostFrequentMerchant)) {
              _tmpMostFrequentMerchant = null;
            } else {
              _tmpMostFrequentMerchant = _cursor.getString(_cursorIndexOfMostFrequentMerchant);
            }
            final LocalDateTime _tmpCalculatedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCalculatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCalculatedDate);
            }
            _tmpCalculatedDate = __converters.toLocalDateTime(_tmp);
            final int _tmpDaysInPeriod;
            _tmpDaysInPeriod = _cursor.getInt(_cursorIndexOfDaysInPeriod);
            final Double _tmpProjectedEndAmount;
            if (_cursor.isNull(_cursorIndexOfProjectedEndAmount)) {
              _tmpProjectedEndAmount = null;
            } else {
              _tmpProjectedEndAmount = _cursor.getDouble(_cursorIndexOfProjectedEndAmount);
            }
            final Double _tmpRecommendedAdjustment;
            if (_cursor.isNull(_cursorIndexOfRecommendedAdjustment)) {
              _tmpRecommendedAdjustment = null;
            } else {
              _tmpRecommendedAdjustment = _cursor.getDouble(_cursorIndexOfRecommendedAdjustment);
            }
            final double _tmpSeasonalityFactor;
            _tmpSeasonalityFactor = _cursor.getDouble(_cursorIndexOfSeasonalityFactor);
            final boolean _tmpIsOutlierPeriod;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsOutlierPeriod);
            _tmpIsOutlierPeriod = _tmp_1 != 0;
            _item = new BudgetAnalytics(_tmpId,_tmpCategoryName,_tmpBudgetPeriod,_tmpBudgetYear,_tmpBudgetMonth,_tmpBudgetWeek,_tmpPlannedAmount,_tmpActualSpent,_tmpVariance,_tmpVariancePercentage,_tmpTrendDirection,_tmpAverageTransactionSize,_tmpTransactionCount,_tmpLargestTransaction,_tmpSmallestTransaction,_tmpMostFrequentMerchant,_tmpCalculatedDate,_tmpDaysInPeriod,_tmpProjectedEndAmount,_tmpRecommendedAdjustment,_tmpSeasonalityFactor,_tmpIsOutlierPeriod);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<BudgetAnalytics>> getAnalyticsByTrend(final String direction) {
    final String _sql = "SELECT * FROM budget_analytics WHERE trendDirection = ? ORDER BY calculatedDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (direction == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, direction);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"budget_analytics"}, new Callable<List<BudgetAnalytics>>() {
      @Override
      @NonNull
      public List<BudgetAnalytics> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryName");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetPeriod");
          final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetYear");
          final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetMonth");
          final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetWeek");
          final int _cursorIndexOfPlannedAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "plannedAmount");
          final int _cursorIndexOfActualSpent = CursorUtil.getColumnIndexOrThrow(_cursor, "actualSpent");
          final int _cursorIndexOfVariance = CursorUtil.getColumnIndexOrThrow(_cursor, "variance");
          final int _cursorIndexOfVariancePercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "variancePercentage");
          final int _cursorIndexOfTrendDirection = CursorUtil.getColumnIndexOrThrow(_cursor, "trendDirection");
          final int _cursorIndexOfAverageTransactionSize = CursorUtil.getColumnIndexOrThrow(_cursor, "averageTransactionSize");
          final int _cursorIndexOfTransactionCount = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionCount");
          final int _cursorIndexOfLargestTransaction = CursorUtil.getColumnIndexOrThrow(_cursor, "largestTransaction");
          final int _cursorIndexOfSmallestTransaction = CursorUtil.getColumnIndexOrThrow(_cursor, "smallestTransaction");
          final int _cursorIndexOfMostFrequentMerchant = CursorUtil.getColumnIndexOrThrow(_cursor, "mostFrequentMerchant");
          final int _cursorIndexOfCalculatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "calculatedDate");
          final int _cursorIndexOfDaysInPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "daysInPeriod");
          final int _cursorIndexOfProjectedEndAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "projectedEndAmount");
          final int _cursorIndexOfRecommendedAdjustment = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendedAdjustment");
          final int _cursorIndexOfSeasonalityFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalityFactor");
          final int _cursorIndexOfIsOutlierPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "isOutlierPeriod");
          final List<BudgetAnalytics> _result = new ArrayList<BudgetAnalytics>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BudgetAnalytics _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCategoryName;
            if (_cursor.isNull(_cursorIndexOfCategoryName)) {
              _tmpCategoryName = null;
            } else {
              _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
            }
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            final int _tmpBudgetYear;
            _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
            final Integer _tmpBudgetMonth;
            if (_cursor.isNull(_cursorIndexOfBudgetMonth)) {
              _tmpBudgetMonth = null;
            } else {
              _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
            }
            final Integer _tmpBudgetWeek;
            if (_cursor.isNull(_cursorIndexOfBudgetWeek)) {
              _tmpBudgetWeek = null;
            } else {
              _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
            }
            final double _tmpPlannedAmount;
            _tmpPlannedAmount = _cursor.getDouble(_cursorIndexOfPlannedAmount);
            final double _tmpActualSpent;
            _tmpActualSpent = _cursor.getDouble(_cursorIndexOfActualSpent);
            final double _tmpVariance;
            _tmpVariance = _cursor.getDouble(_cursorIndexOfVariance);
            final double _tmpVariancePercentage;
            _tmpVariancePercentage = _cursor.getDouble(_cursorIndexOfVariancePercentage);
            final String _tmpTrendDirection;
            if (_cursor.isNull(_cursorIndexOfTrendDirection)) {
              _tmpTrendDirection = null;
            } else {
              _tmpTrendDirection = _cursor.getString(_cursorIndexOfTrendDirection);
            }
            final double _tmpAverageTransactionSize;
            _tmpAverageTransactionSize = _cursor.getDouble(_cursorIndexOfAverageTransactionSize);
            final int _tmpTransactionCount;
            _tmpTransactionCount = _cursor.getInt(_cursorIndexOfTransactionCount);
            final double _tmpLargestTransaction;
            _tmpLargestTransaction = _cursor.getDouble(_cursorIndexOfLargestTransaction);
            final double _tmpSmallestTransaction;
            _tmpSmallestTransaction = _cursor.getDouble(_cursorIndexOfSmallestTransaction);
            final String _tmpMostFrequentMerchant;
            if (_cursor.isNull(_cursorIndexOfMostFrequentMerchant)) {
              _tmpMostFrequentMerchant = null;
            } else {
              _tmpMostFrequentMerchant = _cursor.getString(_cursorIndexOfMostFrequentMerchant);
            }
            final LocalDateTime _tmpCalculatedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCalculatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCalculatedDate);
            }
            _tmpCalculatedDate = __converters.toLocalDateTime(_tmp);
            final int _tmpDaysInPeriod;
            _tmpDaysInPeriod = _cursor.getInt(_cursorIndexOfDaysInPeriod);
            final Double _tmpProjectedEndAmount;
            if (_cursor.isNull(_cursorIndexOfProjectedEndAmount)) {
              _tmpProjectedEndAmount = null;
            } else {
              _tmpProjectedEndAmount = _cursor.getDouble(_cursorIndexOfProjectedEndAmount);
            }
            final Double _tmpRecommendedAdjustment;
            if (_cursor.isNull(_cursorIndexOfRecommendedAdjustment)) {
              _tmpRecommendedAdjustment = null;
            } else {
              _tmpRecommendedAdjustment = _cursor.getDouble(_cursorIndexOfRecommendedAdjustment);
            }
            final double _tmpSeasonalityFactor;
            _tmpSeasonalityFactor = _cursor.getDouble(_cursorIndexOfSeasonalityFactor);
            final boolean _tmpIsOutlierPeriod;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsOutlierPeriod);
            _tmpIsOutlierPeriod = _tmp_1 != 0;
            _item = new BudgetAnalytics(_tmpId,_tmpCategoryName,_tmpBudgetPeriod,_tmpBudgetYear,_tmpBudgetMonth,_tmpBudgetWeek,_tmpPlannedAmount,_tmpActualSpent,_tmpVariance,_tmpVariancePercentage,_tmpTrendDirection,_tmpAverageTransactionSize,_tmpTransactionCount,_tmpLargestTransaction,_tmpSmallestTransaction,_tmpMostFrequentMerchant,_tmpCalculatedDate,_tmpDaysInPeriod,_tmpProjectedEndAmount,_tmpRecommendedAdjustment,_tmpSeasonalityFactor,_tmpIsOutlierPeriod);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<BudgetAnalytics>> getOutlierPeriods() {
    final String _sql = "SELECT * FROM budget_analytics WHERE isOutlierPeriod = 1 ORDER BY calculatedDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"budget_analytics"}, new Callable<List<BudgetAnalytics>>() {
      @Override
      @NonNull
      public List<BudgetAnalytics> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryName");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetPeriod");
          final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetYear");
          final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetMonth");
          final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetWeek");
          final int _cursorIndexOfPlannedAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "plannedAmount");
          final int _cursorIndexOfActualSpent = CursorUtil.getColumnIndexOrThrow(_cursor, "actualSpent");
          final int _cursorIndexOfVariance = CursorUtil.getColumnIndexOrThrow(_cursor, "variance");
          final int _cursorIndexOfVariancePercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "variancePercentage");
          final int _cursorIndexOfTrendDirection = CursorUtil.getColumnIndexOrThrow(_cursor, "trendDirection");
          final int _cursorIndexOfAverageTransactionSize = CursorUtil.getColumnIndexOrThrow(_cursor, "averageTransactionSize");
          final int _cursorIndexOfTransactionCount = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionCount");
          final int _cursorIndexOfLargestTransaction = CursorUtil.getColumnIndexOrThrow(_cursor, "largestTransaction");
          final int _cursorIndexOfSmallestTransaction = CursorUtil.getColumnIndexOrThrow(_cursor, "smallestTransaction");
          final int _cursorIndexOfMostFrequentMerchant = CursorUtil.getColumnIndexOrThrow(_cursor, "mostFrequentMerchant");
          final int _cursorIndexOfCalculatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "calculatedDate");
          final int _cursorIndexOfDaysInPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "daysInPeriod");
          final int _cursorIndexOfProjectedEndAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "projectedEndAmount");
          final int _cursorIndexOfRecommendedAdjustment = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendedAdjustment");
          final int _cursorIndexOfSeasonalityFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalityFactor");
          final int _cursorIndexOfIsOutlierPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "isOutlierPeriod");
          final List<BudgetAnalytics> _result = new ArrayList<BudgetAnalytics>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BudgetAnalytics _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCategoryName;
            if (_cursor.isNull(_cursorIndexOfCategoryName)) {
              _tmpCategoryName = null;
            } else {
              _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
            }
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            final int _tmpBudgetYear;
            _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
            final Integer _tmpBudgetMonth;
            if (_cursor.isNull(_cursorIndexOfBudgetMonth)) {
              _tmpBudgetMonth = null;
            } else {
              _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
            }
            final Integer _tmpBudgetWeek;
            if (_cursor.isNull(_cursorIndexOfBudgetWeek)) {
              _tmpBudgetWeek = null;
            } else {
              _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
            }
            final double _tmpPlannedAmount;
            _tmpPlannedAmount = _cursor.getDouble(_cursorIndexOfPlannedAmount);
            final double _tmpActualSpent;
            _tmpActualSpent = _cursor.getDouble(_cursorIndexOfActualSpent);
            final double _tmpVariance;
            _tmpVariance = _cursor.getDouble(_cursorIndexOfVariance);
            final double _tmpVariancePercentage;
            _tmpVariancePercentage = _cursor.getDouble(_cursorIndexOfVariancePercentage);
            final String _tmpTrendDirection;
            if (_cursor.isNull(_cursorIndexOfTrendDirection)) {
              _tmpTrendDirection = null;
            } else {
              _tmpTrendDirection = _cursor.getString(_cursorIndexOfTrendDirection);
            }
            final double _tmpAverageTransactionSize;
            _tmpAverageTransactionSize = _cursor.getDouble(_cursorIndexOfAverageTransactionSize);
            final int _tmpTransactionCount;
            _tmpTransactionCount = _cursor.getInt(_cursorIndexOfTransactionCount);
            final double _tmpLargestTransaction;
            _tmpLargestTransaction = _cursor.getDouble(_cursorIndexOfLargestTransaction);
            final double _tmpSmallestTransaction;
            _tmpSmallestTransaction = _cursor.getDouble(_cursorIndexOfSmallestTransaction);
            final String _tmpMostFrequentMerchant;
            if (_cursor.isNull(_cursorIndexOfMostFrequentMerchant)) {
              _tmpMostFrequentMerchant = null;
            } else {
              _tmpMostFrequentMerchant = _cursor.getString(_cursorIndexOfMostFrequentMerchant);
            }
            final LocalDateTime _tmpCalculatedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCalculatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCalculatedDate);
            }
            _tmpCalculatedDate = __converters.toLocalDateTime(_tmp);
            final int _tmpDaysInPeriod;
            _tmpDaysInPeriod = _cursor.getInt(_cursorIndexOfDaysInPeriod);
            final Double _tmpProjectedEndAmount;
            if (_cursor.isNull(_cursorIndexOfProjectedEndAmount)) {
              _tmpProjectedEndAmount = null;
            } else {
              _tmpProjectedEndAmount = _cursor.getDouble(_cursorIndexOfProjectedEndAmount);
            }
            final Double _tmpRecommendedAdjustment;
            if (_cursor.isNull(_cursorIndexOfRecommendedAdjustment)) {
              _tmpRecommendedAdjustment = null;
            } else {
              _tmpRecommendedAdjustment = _cursor.getDouble(_cursorIndexOfRecommendedAdjustment);
            }
            final double _tmpSeasonalityFactor;
            _tmpSeasonalityFactor = _cursor.getDouble(_cursorIndexOfSeasonalityFactor);
            final boolean _tmpIsOutlierPeriod;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsOutlierPeriod);
            _tmpIsOutlierPeriod = _tmp_1 != 0;
            _item = new BudgetAnalytics(_tmpId,_tmpCategoryName,_tmpBudgetPeriod,_tmpBudgetYear,_tmpBudgetMonth,_tmpBudgetWeek,_tmpPlannedAmount,_tmpActualSpent,_tmpVariance,_tmpVariancePercentage,_tmpTrendDirection,_tmpAverageTransactionSize,_tmpTransactionCount,_tmpLargestTransaction,_tmpSmallestTransaction,_tmpMostFrequentMerchant,_tmpCalculatedDate,_tmpDaysInPeriod,_tmpProjectedEndAmount,_tmpRecommendedAdjustment,_tmpSeasonalityFactor,_tmpIsOutlierPeriod);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getCategoryVarianceAverages(
      final Continuation<? super List<CategoryVarianceAverage>> $completion) {
    final String _sql = "SELECT categoryName, AVG(variancePercentage) as avgVariance FROM budget_analytics GROUP BY categoryName ORDER BY avgVariance DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CategoryVarianceAverage>>() {
      @Override
      @NonNull
      public List<CategoryVarianceAverage> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCategoryName = 0;
          final int _cursorIndexOfAvgVariance = 1;
          final List<CategoryVarianceAverage> _result = new ArrayList<CategoryVarianceAverage>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CategoryVarianceAverage _item;
            final String _tmpCategoryName;
            if (_cursor.isNull(_cursorIndexOfCategoryName)) {
              _tmpCategoryName = null;
            } else {
              _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
            }
            final double _tmpAvgVariance;
            _tmpAvgVariance = _cursor.getDouble(_cursorIndexOfAvgVariance);
            _item = new CategoryVarianceAverage(_tmpCategoryName,_tmpAvgVariance);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAverageVarianceForCategory(final String categoryName,
      final Continuation<? super Double> $completion) {
    final String _sql = "SELECT AVG(variancePercentage) FROM budget_analytics WHERE categoryName = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (categoryName == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, categoryName);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getRecentAnalyticsForCategory(final String categoryName, final int limit,
      final Continuation<? super List<BudgetAnalytics>> $completion) {
    final String _sql = "SELECT * FROM budget_analytics WHERE categoryName = ? ORDER BY calculatedDate DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (categoryName == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, categoryName);
    }
    _argIndex = 2;
    _statement.bindLong(_argIndex, limit);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<BudgetAnalytics>>() {
      @Override
      @NonNull
      public List<BudgetAnalytics> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryName");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetPeriod");
          final int _cursorIndexOfBudgetYear = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetYear");
          final int _cursorIndexOfBudgetMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetMonth");
          final int _cursorIndexOfBudgetWeek = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetWeek");
          final int _cursorIndexOfPlannedAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "plannedAmount");
          final int _cursorIndexOfActualSpent = CursorUtil.getColumnIndexOrThrow(_cursor, "actualSpent");
          final int _cursorIndexOfVariance = CursorUtil.getColumnIndexOrThrow(_cursor, "variance");
          final int _cursorIndexOfVariancePercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "variancePercentage");
          final int _cursorIndexOfTrendDirection = CursorUtil.getColumnIndexOrThrow(_cursor, "trendDirection");
          final int _cursorIndexOfAverageTransactionSize = CursorUtil.getColumnIndexOrThrow(_cursor, "averageTransactionSize");
          final int _cursorIndexOfTransactionCount = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionCount");
          final int _cursorIndexOfLargestTransaction = CursorUtil.getColumnIndexOrThrow(_cursor, "largestTransaction");
          final int _cursorIndexOfSmallestTransaction = CursorUtil.getColumnIndexOrThrow(_cursor, "smallestTransaction");
          final int _cursorIndexOfMostFrequentMerchant = CursorUtil.getColumnIndexOrThrow(_cursor, "mostFrequentMerchant");
          final int _cursorIndexOfCalculatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "calculatedDate");
          final int _cursorIndexOfDaysInPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "daysInPeriod");
          final int _cursorIndexOfProjectedEndAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "projectedEndAmount");
          final int _cursorIndexOfRecommendedAdjustment = CursorUtil.getColumnIndexOrThrow(_cursor, "recommendedAdjustment");
          final int _cursorIndexOfSeasonalityFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalityFactor");
          final int _cursorIndexOfIsOutlierPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "isOutlierPeriod");
          final List<BudgetAnalytics> _result = new ArrayList<BudgetAnalytics>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BudgetAnalytics _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCategoryName;
            if (_cursor.isNull(_cursorIndexOfCategoryName)) {
              _tmpCategoryName = null;
            } else {
              _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
            }
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            final int _tmpBudgetYear;
            _tmpBudgetYear = _cursor.getInt(_cursorIndexOfBudgetYear);
            final Integer _tmpBudgetMonth;
            if (_cursor.isNull(_cursorIndexOfBudgetMonth)) {
              _tmpBudgetMonth = null;
            } else {
              _tmpBudgetMonth = _cursor.getInt(_cursorIndexOfBudgetMonth);
            }
            final Integer _tmpBudgetWeek;
            if (_cursor.isNull(_cursorIndexOfBudgetWeek)) {
              _tmpBudgetWeek = null;
            } else {
              _tmpBudgetWeek = _cursor.getInt(_cursorIndexOfBudgetWeek);
            }
            final double _tmpPlannedAmount;
            _tmpPlannedAmount = _cursor.getDouble(_cursorIndexOfPlannedAmount);
            final double _tmpActualSpent;
            _tmpActualSpent = _cursor.getDouble(_cursorIndexOfActualSpent);
            final double _tmpVariance;
            _tmpVariance = _cursor.getDouble(_cursorIndexOfVariance);
            final double _tmpVariancePercentage;
            _tmpVariancePercentage = _cursor.getDouble(_cursorIndexOfVariancePercentage);
            final String _tmpTrendDirection;
            if (_cursor.isNull(_cursorIndexOfTrendDirection)) {
              _tmpTrendDirection = null;
            } else {
              _tmpTrendDirection = _cursor.getString(_cursorIndexOfTrendDirection);
            }
            final double _tmpAverageTransactionSize;
            _tmpAverageTransactionSize = _cursor.getDouble(_cursorIndexOfAverageTransactionSize);
            final int _tmpTransactionCount;
            _tmpTransactionCount = _cursor.getInt(_cursorIndexOfTransactionCount);
            final double _tmpLargestTransaction;
            _tmpLargestTransaction = _cursor.getDouble(_cursorIndexOfLargestTransaction);
            final double _tmpSmallestTransaction;
            _tmpSmallestTransaction = _cursor.getDouble(_cursorIndexOfSmallestTransaction);
            final String _tmpMostFrequentMerchant;
            if (_cursor.isNull(_cursorIndexOfMostFrequentMerchant)) {
              _tmpMostFrequentMerchant = null;
            } else {
              _tmpMostFrequentMerchant = _cursor.getString(_cursorIndexOfMostFrequentMerchant);
            }
            final LocalDateTime _tmpCalculatedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCalculatedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCalculatedDate);
            }
            _tmpCalculatedDate = __converters.toLocalDateTime(_tmp);
            final int _tmpDaysInPeriod;
            _tmpDaysInPeriod = _cursor.getInt(_cursorIndexOfDaysInPeriod);
            final Double _tmpProjectedEndAmount;
            if (_cursor.isNull(_cursorIndexOfProjectedEndAmount)) {
              _tmpProjectedEndAmount = null;
            } else {
              _tmpProjectedEndAmount = _cursor.getDouble(_cursorIndexOfProjectedEndAmount);
            }
            final Double _tmpRecommendedAdjustment;
            if (_cursor.isNull(_cursorIndexOfRecommendedAdjustment)) {
              _tmpRecommendedAdjustment = null;
            } else {
              _tmpRecommendedAdjustment = _cursor.getDouble(_cursorIndexOfRecommendedAdjustment);
            }
            final double _tmpSeasonalityFactor;
            _tmpSeasonalityFactor = _cursor.getDouble(_cursorIndexOfSeasonalityFactor);
            final boolean _tmpIsOutlierPeriod;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsOutlierPeriod);
            _tmpIsOutlierPeriod = _tmp_1 != 0;
            _item = new BudgetAnalytics(_tmpId,_tmpCategoryName,_tmpBudgetPeriod,_tmpBudgetYear,_tmpBudgetMonth,_tmpBudgetWeek,_tmpPlannedAmount,_tmpActualSpent,_tmpVariance,_tmpVariancePercentage,_tmpTrendDirection,_tmpAverageTransactionSize,_tmpTransactionCount,_tmpLargestTransaction,_tmpSmallestTransaction,_tmpMostFrequentMerchant,_tmpCalculatedDate,_tmpDaysInPeriod,_tmpProjectedEndAmount,_tmpRecommendedAdjustment,_tmpSeasonalityFactor,_tmpIsOutlierPeriod);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getHighVarianceCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM budget_analytics WHERE variancePercentage > 20";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getIncreasingTrendCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM budget_analytics WHERE trendDirection = 'increasing'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
