package com.focusflow.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u0006\n\u0002\b\b\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u001c\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\r0\f2\u0006\u0010\u000e\u001a\u00020\u000fH\'J\u001c\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\r0\f2\u0006\u0010\u0011\u001a\u00020\tH\'J\u001c\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\r0\f2\u0006\u0010\b\u001a\u00020\tH\'J\u001c\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00050\r2\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ$\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\r0\f2\u0006\u0010\u0015\u001a\u00020\u000f2\u0006\u0010\u0016\u001a\u00020\u000fH\'J\u0018\u0010\u0017\u001a\u0004\u0018\u00010\u00182\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0018\u0010\u0019\u001a\u0004\u0018\u00010\u00182\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0018\u0010\u001a\u001a\u0004\u0018\u00010\u00182\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u001b\u001a\u00020\t2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001c\u0010\u001c\u001a\u00020\u00032\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00050\rH\u00a7@\u00a2\u0006\u0002\u0010\u001eJ\u0016\u0010\u001f\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006\u00a8\u0006 "}, d2 = {"Lcom/focusflow/data/dao/PaymentScheduleDao;", "", "deletePaymentSchedule", "", "paymentSchedule", "Lcom/focusflow/data/model/PaymentSchedule;", "(Lcom/focusflow/data/model/PaymentSchedule;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deletePaymentSchedulesByPlan", "planId", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getOverduePayments", "Lkotlinx/coroutines/flow/Flow;", "", "date", "Lkotlinx/datetime/LocalDate;", "getPaymentScheduleByCard", "cardId", "getPaymentScheduleByPlan", "getPaymentScheduleByPlanSync", "getPaymentsDueInRange", "startDate", "endDate", "getTotalInterestPayments", "", "getTotalPrincipalPayments", "getTotalScheduledPayments", "insertPaymentSchedule", "insertPaymentSchedules", "paymentSchedules", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updatePaymentSchedule", "app_release"})
@androidx.room.Dao
public abstract interface PaymentScheduleDao {
    
    @androidx.room.Query(value = "SELECT * FROM payment_schedules WHERE payoffPlanId = :planId ORDER BY month ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.PaymentSchedule>> getPaymentScheduleByPlan(long planId);
    
    @androidx.room.Query(value = "SELECT * FROM payment_schedules WHERE payoffPlanId = :planId ORDER BY month ASC")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getPaymentScheduleByPlanSync(long planId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.PaymentSchedule>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM payment_schedules WHERE creditCardId = :cardId ORDER BY month ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.PaymentSchedule>> getPaymentScheduleByCard(long cardId);
    
    @androidx.room.Query(value = "SELECT * FROM payment_schedules WHERE dueDate BETWEEN :startDate AND :endDate ORDER BY dueDate ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.PaymentSchedule>> getPaymentsDueInRange(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate startDate, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate endDate);
    
    @androidx.room.Query(value = "SELECT * FROM payment_schedules WHERE dueDate <= :date ORDER BY dueDate ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.PaymentSchedule>> getOverduePayments(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate date);
    
    @androidx.room.Query(value = "SELECT SUM(scheduledPayment) FROM payment_schedules WHERE payoffPlanId = :planId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalScheduledPayments(long planId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT SUM(principalAmount) FROM payment_schedules WHERE payoffPlanId = :planId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalPrincipalPayments(long planId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT SUM(interestAmount) FROM payment_schedules WHERE payoffPlanId = :planId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalInterestPayments(long planId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Insert
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertPaymentSchedule(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.PaymentSchedule paymentSchedule, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Insert
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertPaymentSchedules(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.PaymentSchedule> paymentSchedules, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updatePaymentSchedule(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.PaymentSchedule paymentSchedule, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deletePaymentSchedule(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.PaymentSchedule paymentSchedule, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM payment_schedules WHERE payoffPlanId = :planId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deletePaymentSchedulesByPlan(long planId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}