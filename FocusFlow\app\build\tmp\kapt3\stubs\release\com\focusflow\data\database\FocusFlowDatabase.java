package com.focusflow.data.database;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0098\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\'\u0018\u0000 12\u00020\u0001:\u00011B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H&J\b\u0010\u0005\u001a\u00020\u0006H&J\b\u0010\u0007\u001a\u00020\bH&J\b\u0010\t\u001a\u00020\nH&J\b\u0010\u000b\u001a\u00020\fH&J\b\u0010\r\u001a\u00020\u000eH&J\b\u0010\u000f\u001a\u00020\u0010H&J\b\u0010\u0011\u001a\u00020\u0012H&J\b\u0010\u0013\u001a\u00020\u0014H&J\b\u0010\u0015\u001a\u00020\u0016H&J\b\u0010\u0017\u001a\u00020\u0018H&J\b\u0010\u0019\u001a\u00020\u001aH&J\b\u0010\u001b\u001a\u00020\u001cH&J\b\u0010\u001d\u001a\u00020\u001eH&J\b\u0010\u001f\u001a\u00020 H&J\b\u0010!\u001a\u00020\"H&J\b\u0010#\u001a\u00020$H&J\b\u0010%\u001a\u00020&H&J\b\u0010\'\u001a\u00020(H&J\b\u0010)\u001a\u00020*H&J\b\u0010+\u001a\u00020,H&J\b\u0010-\u001a\u00020.H&J\b\u0010/\u001a\u000200H&\u00a8\u00062"}, d2 = {"Lcom/focusflow/data/database/FocusFlowDatabase;", "Landroidx/room/RoomDatabase;", "()V", "accountabilityContactDao", "Lcom/focusflow/data/dao/AccountabilityContactDao;", "achievementDao", "Lcom/focusflow/data/dao/AchievementDao;", "aiInteractionDao", "Lcom/focusflow/data/dao/AIInteractionDao;", "alternativeProductDao", "Lcom/focusflow/data/dao/AlternativeProductDao;", "budgetAnalyticsDao", "Lcom/focusflow/data/dao/BudgetAnalyticsDao;", "budgetCategoryDao", "Lcom/focusflow/data/dao/BudgetCategoryDao;", "budgetRecommendationDao", "Lcom/focusflow/data/dao/BudgetRecommendationDao;", "creditCardDao", "Lcom/focusflow/data/dao/CreditCardDao;", "dashboardWidgetDao", "Lcom/focusflow/data/dao/DashboardWidgetDao;", "expenseDao", "Lcom/focusflow/data/dao/ExpenseDao;", "focusSessionDao", "Lcom/focusflow/data/dao/FocusSessionDao;", "habitLogDao", "Lcom/focusflow/data/dao/HabitLogDao;", "paymentScheduleDao", "Lcom/focusflow/data/dao/PaymentScheduleDao;", "payoffMilestoneDao", "Lcom/focusflow/data/dao/PayoffMilestoneDao;", "payoffPlanDao", "Lcom/focusflow/data/dao/PayoffPlanDao;", "spendingPatternDao", "Lcom/focusflow/data/dao/SpendingPatternDao;", "spendingReflectionDao", "Lcom/focusflow/data/dao/SpendingReflectionDao;", "taskDao", "Lcom/focusflow/data/dao/TaskDao;", "userPreferencesDao", "Lcom/focusflow/data/dao/UserPreferencesDao;", "userStatsDao", "Lcom/focusflow/data/dao/UserStatsDao;", "virtualPetDao", "Lcom/focusflow/data/dao/VirtualPetDao;", "voiceCommandDao", "Lcom/focusflow/data/dao/VoiceCommandDao;", "wishlistItemDao", "Lcom/focusflow/data/dao/WishlistItemDao;", "Companion", "app_release"})
@androidx.room.Database(entities = {com.focusflow.data.model.Expense.class, com.focusflow.data.model.CreditCard.class, com.focusflow.data.model.BudgetCategory.class, com.focusflow.data.model.HabitLog.class, com.focusflow.data.model.Task.class, com.focusflow.data.model.AIInteraction.class, com.focusflow.data.model.UserPreferences.class, com.focusflow.data.model.Achievement.class, com.focusflow.data.model.UserStats.class, com.focusflow.data.model.VirtualPet.class, com.focusflow.data.model.WishlistItem.class, com.focusflow.data.model.BudgetRecommendation.class, com.focusflow.data.model.SpendingReflection.class, com.focusflow.data.model.BudgetAnalytics.class, com.focusflow.data.model.FocusSession.class, com.focusflow.data.model.SpendingPattern.class, com.focusflow.data.model.AlternativeProduct.class, com.focusflow.data.model.AccountabilityContact.class, com.focusflow.data.model.DashboardWidget.class, com.focusflow.data.model.VoiceCommand.class, com.focusflow.data.model.PayoffPlan.class, com.focusflow.data.model.PaymentSchedule.class, com.focusflow.data.model.PayoffMilestone.class}, version = 8, exportSchema = false)
@androidx.room.TypeConverters(value = {com.focusflow.data.database.Converters.class})
public abstract class FocusFlowDatabase extends androidx.room.RoomDatabase {
    @kotlin.jvm.Volatile
    @org.jetbrains.annotations.Nullable
    private static volatile com.focusflow.data.database.FocusFlowDatabase INSTANCE;
    @org.jetbrains.annotations.NotNull
    public static final com.focusflow.data.database.FocusFlowDatabase.Companion Companion = null;
    
    public FocusFlowDatabase() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.ExpenseDao expenseDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.CreditCardDao creditCardDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.BudgetCategoryDao budgetCategoryDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.HabitLogDao habitLogDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.TaskDao taskDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.AIInteractionDao aiInteractionDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.UserPreferencesDao userPreferencesDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.AchievementDao achievementDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.UserStatsDao userStatsDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.VirtualPetDao virtualPetDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.WishlistItemDao wishlistItemDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.BudgetRecommendationDao budgetRecommendationDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.SpendingReflectionDao spendingReflectionDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.BudgetAnalyticsDao budgetAnalyticsDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.FocusSessionDao focusSessionDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.SpendingPatternDao spendingPatternDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.AlternativeProductDao alternativeProductDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.AccountabilityContactDao accountabilityContactDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.DashboardWidgetDao dashboardWidgetDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.VoiceCommandDao voiceCommandDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.PayoffPlanDao payoffPlanDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.PaymentScheduleDao paymentScheduleDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.data.dao.PayoffMilestoneDao payoffMilestoneDao();
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u0007R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/focusflow/data/database/FocusFlowDatabase$Companion;", "", "()V", "INSTANCE", "Lcom/focusflow/data/database/FocusFlowDatabase;", "getDatabase", "context", "Landroid/content/Context;", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.data.database.FocusFlowDatabase getDatabase(@org.jetbrains.annotations.NotNull
        android.content.Context context) {
            return null;
        }
    }
}