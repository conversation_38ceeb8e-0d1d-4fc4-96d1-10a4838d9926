package com.focusflow.data.repository;

import com.focusflow.data.dao.BudgetAnalyticsDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class BudgetAnalyticsRepository_Factory implements Factory<BudgetAnalyticsRepository> {
  private final Provider<BudgetAnalyticsDao> budgetAnalyticsDaoProvider;

  public BudgetAnalyticsRepository_Factory(
      Provider<BudgetAnalyticsDao> budgetAnalyticsDaoProvider) {
    this.budgetAnalyticsDaoProvider = budgetAnalyticsDaoProvider;
  }

  @Override
  public BudgetAnalyticsRepository get() {
    return newInstance(budgetAnalyticsDaoProvider.get());
  }

  public static BudgetAnalyticsRepository_Factory create(
      Provider<BudgetAnalyticsDao> budgetAnalyticsDaoProvider) {
    return new BudgetAnalyticsRepository_Factory(budgetAnalyticsDaoProvider);
  }

  public static BudgetAnalyticsRepository newInstance(BudgetAnalyticsDao budgetAnalyticsDao) {
    return new BudgetAnalyticsRepository(budgetAnalyticsDao);
  }
}
