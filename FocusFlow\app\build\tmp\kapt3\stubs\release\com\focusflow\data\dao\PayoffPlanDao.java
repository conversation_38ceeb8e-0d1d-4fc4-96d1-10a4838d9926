package com.focusflow.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\u0003\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u000e\u0010\u000b\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\rJ\u0014\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00100\u000fH\'J\u0010\u0010\u0011\u001a\u0004\u0018\u00010\tH\u00a7@\u00a2\u0006\u0002\u0010\rJ\u0010\u0010\u0012\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\u000fH\'J\u0018\u0010\u0013\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0014\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001c\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00100\u000f2\u0006\u0010\u0016\u001a\u00020\u0017H\'J\u0016\u0010\u0018\u001a\u00020\u00052\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u0019\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\n\u00a8\u0006\u001a"}, d2 = {"Lcom/focusflow/data/dao/PayoffPlanDao;", "", "deactivateOtherPlans", "", "activeId", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deletePayoffPlan", "payoffPlan", "Lcom/focusflow/data/model/PayoffPlan;", "(Lcom/focusflow/data/model/PayoffPlan;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActivePayoffPlanCount", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllActivePayoffPlans", "Lkotlinx/coroutines/flow/Flow;", "", "getCurrentActivePayoffPlan", "getCurrentActivePayoffPlanFlow", "getPayoffPlanById", "planId", "getPayoffPlansByStrategy", "strategy", "", "insertPayoffPlan", "updatePayoffPlan", "app_release"})
@androidx.room.Dao
public abstract interface PayoffPlanDao {
    
    @androidx.room.Query(value = "SELECT * FROM payoff_plans WHERE isActive = 1 ORDER BY createdAt DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.PayoffPlan>> getAllActivePayoffPlans();
    
    @androidx.room.Query(value = "SELECT * FROM payoff_plans WHERE id = :planId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getPayoffPlanById(long planId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.PayoffPlan> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM payoff_plans WHERE isActive = 1 ORDER BY createdAt DESC LIMIT 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getCurrentActivePayoffPlan(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.PayoffPlan> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM payoff_plans WHERE isActive = 1 ORDER BY createdAt DESC LIMIT 1")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<com.focusflow.data.model.PayoffPlan> getCurrentActivePayoffPlanFlow();
    
    @androidx.room.Insert
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertPayoffPlan(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.PayoffPlan payoffPlan, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updatePayoffPlan(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.PayoffPlan payoffPlan, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deletePayoffPlan(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.PayoffPlan payoffPlan, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE payoff_plans SET isActive = 0 WHERE id != :activeId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deactivateOtherPlans(long activeId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM payoff_plans WHERE isActive = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getActivePayoffPlanCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM payoff_plans WHERE strategy = :strategy AND isActive = 1")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.PayoffPlan>> getPayoffPlansByStrategy(@org.jetbrains.annotations.NotNull
    java.lang.String strategy);
}