package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.focusflow.FocusFlowApplication",
    rootPackage = "com.focusflow",
    originatingRoot = "com.focusflow.FocusFlowApplication",
    originatingRootPackage = "com.focusflow",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "FocusFlowApplication",
    originatingRootSimpleNames = "FocusFlowApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_focusflow_FocusFlowApplication {
}
