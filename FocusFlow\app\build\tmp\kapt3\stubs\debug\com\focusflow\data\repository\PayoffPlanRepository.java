package com.focusflow.data.repository;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u000e\b\u0007\u0018\u00002\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ2\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000e2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\u000eH\u0086@\u00a2\u0006\u0002\u0010\u0012J\u0016\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010\u0017\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010\u0018\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010\u0019\u001a\u00020\u00142\u0006\u0010\u001a\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\u001bJ\u0012\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000e0\u001dJ\u0012\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u000e0\u001dJ\u0010\u0010\u001f\u001a\u0004\u0018\u00010\fH\u0086@\u00a2\u0006\u0002\u0010 J\u000e\u0010!\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\f0\u001dJ\u001a\u0010\"\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u000e0\u001d2\u0006\u0010\u0015\u001a\u00020\nJ\u001c\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00110\u000e2\u0006\u0010\u0015\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u0016J\u0018\u0010$\u001a\u0004\u0018\u00010\u00112\u0006\u0010\u0015\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u0016J\u001a\u0010%\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0\u001d2\u0006\u0010&\u001a\u00020\nJ\u001a\u0010\'\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0\u001d2\u0006\u0010\u0015\u001a\u00020\nJ\u001c\u0010(\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000e2\u0006\u0010\u0015\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u0016J\"\u0010)\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0\u001d2\u0006\u0010*\u001a\u00020+2\u0006\u0010,\u001a\u00020+J\u0018\u0010-\u001a\u0004\u0018\u00010\f2\u0006\u0010\u0015\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u0016J\u001a\u0010.\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000e0\u001d2\u0006\u0010/\u001a\u000200J\u001a\u00101\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u000e0\u001d2\u0006\u00102\u001a\u00020+J\u001c\u00103\u001a\u00020\u00142\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\u000eH\u0086@\u00a2\u0006\u0002\u00104J\u001c\u00105\u001a\u00020\u00142\f\u00106\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000eH\u0086@\u00a2\u0006\u0002\u00104J\u0016\u00107\u001a\u00020\n2\u0006\u0010\u001a\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\u001bJ\u001e\u00108\u001a\u00020\u00142\u0006\u00109\u001a\u00020\n2\u0006\u0010:\u001a\u00020+H\u0086@\u00a2\u0006\u0002\u0010;J\u0016\u0010<\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010=\u001a\u00020\u00142\u0006\u0010\u001a\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\u001bR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006>"}, d2 = {"Lcom/focusflow/data/repository/PayoffPlanRepository;", "", "payoffPlanDao", "Lcom/focusflow/data/dao/PayoffPlanDao;", "paymentScheduleDao", "Lcom/focusflow/data/dao/PaymentScheduleDao;", "payoffMilestoneDao", "Lcom/focusflow/data/dao/PayoffMilestoneDao;", "(Lcom/focusflow/data/dao/PayoffPlanDao;Lcom/focusflow/data/dao/PaymentScheduleDao;Lcom/focusflow/data/dao/PayoffMilestoneDao;)V", "createCompletePayoffPlan", "", "plan", "Lcom/focusflow/data/model/PayoffPlan;", "schedules", "", "Lcom/focusflow/data/model/PaymentSchedule;", "milestones", "Lcom/focusflow/data/model/PayoffMilestone;", "(Lcom/focusflow/data/model/PayoffPlan;Ljava/util/List;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteCompletePayoffPlan", "", "planId", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteMilestonesByPlan", "deletePaymentSchedulesByPlan", "deletePayoffPlan", "payoffPlan", "(Lcom/focusflow/data/model/PayoffPlan;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllActivePayoffPlans", "Lkotlinx/coroutines/flow/Flow;", "getCompletedMilestones", "getCurrentActivePayoffPlan", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCurrentActivePayoffPlanFlow", "getMilestonesByPlan", "getMilestonesByPlanSync", "getNextMilestone", "getPaymentScheduleByCard", "cardId", "getPaymentScheduleByPlan", "getPaymentScheduleByPlanSync", "getPaymentsDueInRange", "startDate", "Lkotlinx/datetime/LocalDate;", "endDate", "getPayoffPlanById", "getPayoffPlansByStrategy", "strategy", "", "getUpcomingMilestones", "date", "insertMilestones", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertPaymentSchedules", "paymentSchedules", "insertPayoffPlan", "markMilestoneCompleted", "milestoneId", "completedDate", "(JLkotlinx/datetime/LocalDate;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setActivePayoffPlan", "updatePayoffPlan", "app_debug"})
public final class PayoffPlanRepository {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.dao.PayoffPlanDao payoffPlanDao = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.dao.PaymentScheduleDao paymentScheduleDao = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.dao.PayoffMilestoneDao payoffMilestoneDao = null;
    
    @javax.inject.Inject
    public PayoffPlanRepository(@org.jetbrains.annotations.NotNull
    com.focusflow.data.dao.PayoffPlanDao payoffPlanDao, @org.jetbrains.annotations.NotNull
    com.focusflow.data.dao.PaymentScheduleDao paymentScheduleDao, @org.jetbrains.annotations.NotNull
    com.focusflow.data.dao.PayoffMilestoneDao payoffMilestoneDao) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.PayoffPlan>> getAllActivePayoffPlans() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getPayoffPlanById(long planId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.PayoffPlan> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getCurrentActivePayoffPlan(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.PayoffPlan> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<com.focusflow.data.model.PayoffPlan> getCurrentActivePayoffPlanFlow() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object insertPayoffPlan(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.PayoffPlan payoffPlan, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object updatePayoffPlan(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.PayoffPlan payoffPlan, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deletePayoffPlan(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.PayoffPlan payoffPlan, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object setActivePayoffPlan(long planId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.PayoffPlan>> getPayoffPlansByStrategy(@org.jetbrains.annotations.NotNull
    java.lang.String strategy) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.PaymentSchedule>> getPaymentScheduleByPlan(long planId) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getPaymentScheduleByPlanSync(long planId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.PaymentSchedule>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.PaymentSchedule>> getPaymentScheduleByCard(long cardId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.PaymentSchedule>> getPaymentsDueInRange(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate startDate, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate endDate) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object insertPaymentSchedules(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.PaymentSchedule> paymentSchedules, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deletePaymentSchedulesByPlan(long planId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.PayoffMilestone>> getMilestonesByPlan(long planId) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getMilestonesByPlanSync(long planId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.PayoffMilestone>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.PayoffMilestone>> getUpcomingMilestones(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate date) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.PayoffMilestone>> getCompletedMilestones() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getNextMilestone(long planId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.PayoffMilestone> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object insertMilestones(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.PayoffMilestone> milestones, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object markMilestoneCompleted(long milestoneId, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate completedDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deleteMilestonesByPlan(long planId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object createCompletePayoffPlan(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.PayoffPlan plan, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.PaymentSchedule> schedules, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.PayoffMilestone> milestones, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deleteCompletePayoffPlan(long planId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}