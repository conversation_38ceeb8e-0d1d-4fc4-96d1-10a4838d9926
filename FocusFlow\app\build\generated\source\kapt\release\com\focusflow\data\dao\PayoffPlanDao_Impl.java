package com.focusflow.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.focusflow.data.database.Converters;
import com.focusflow.data.model.PayoffPlan;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;
import kotlinx.datetime.LocalDate;
import kotlinx.datetime.LocalDateTime;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class PayoffPlanDao_Impl implements PayoffPlanDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<PayoffPlan> __insertionAdapterOfPayoffPlan;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<PayoffPlan> __deletionAdapterOfPayoffPlan;

  private final EntityDeletionOrUpdateAdapter<PayoffPlan> __updateAdapterOfPayoffPlan;

  private final SharedSQLiteStatement __preparedStmtOfDeactivateOtherPlans;

  public PayoffPlanDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfPayoffPlan = new EntityInsertionAdapter<PayoffPlan>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `payoff_plans` (`id`,`name`,`strategy`,`totalExtraPayment`,`totalMonths`,`totalInterestSaved`,`totalPayments`,`createdAt`,`isActive`,`targetPayoffDate`,`monthlyBudgetAllocation`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PayoffPlan entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getName());
        }
        if (entity.getStrategy() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getStrategy());
        }
        statement.bindDouble(4, entity.getTotalExtraPayment());
        statement.bindLong(5, entity.getTotalMonths());
        statement.bindDouble(6, entity.getTotalInterestSaved());
        statement.bindDouble(7, entity.getTotalPayments());
        final String _tmp = __converters.fromLocalDateTime(entity.getCreatedAt());
        if (_tmp == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp);
        }
        final int _tmp_1 = entity.isActive() ? 1 : 0;
        statement.bindLong(9, _tmp_1);
        final String _tmp_2 = __converters.fromLocalDate(entity.getTargetPayoffDate());
        if (_tmp_2 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_2);
        }
        if (entity.getMonthlyBudgetAllocation() == null) {
          statement.bindNull(11);
        } else {
          statement.bindDouble(11, entity.getMonthlyBudgetAllocation());
        }
      }
    };
    this.__deletionAdapterOfPayoffPlan = new EntityDeletionOrUpdateAdapter<PayoffPlan>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `payoff_plans` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PayoffPlan entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfPayoffPlan = new EntityDeletionOrUpdateAdapter<PayoffPlan>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `payoff_plans` SET `id` = ?,`name` = ?,`strategy` = ?,`totalExtraPayment` = ?,`totalMonths` = ?,`totalInterestSaved` = ?,`totalPayments` = ?,`createdAt` = ?,`isActive` = ?,`targetPayoffDate` = ?,`monthlyBudgetAllocation` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PayoffPlan entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getName());
        }
        if (entity.getStrategy() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getStrategy());
        }
        statement.bindDouble(4, entity.getTotalExtraPayment());
        statement.bindLong(5, entity.getTotalMonths());
        statement.bindDouble(6, entity.getTotalInterestSaved());
        statement.bindDouble(7, entity.getTotalPayments());
        final String _tmp = __converters.fromLocalDateTime(entity.getCreatedAt());
        if (_tmp == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp);
        }
        final int _tmp_1 = entity.isActive() ? 1 : 0;
        statement.bindLong(9, _tmp_1);
        final String _tmp_2 = __converters.fromLocalDate(entity.getTargetPayoffDate());
        if (_tmp_2 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_2);
        }
        if (entity.getMonthlyBudgetAllocation() == null) {
          statement.bindNull(11);
        } else {
          statement.bindDouble(11, entity.getMonthlyBudgetAllocation());
        }
        statement.bindLong(12, entity.getId());
      }
    };
    this.__preparedStmtOfDeactivateOtherPlans = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE payoff_plans SET isActive = 0 WHERE id != ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertPayoffPlan(final PayoffPlan payoffPlan,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfPayoffPlan.insertAndReturnId(payoffPlan);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deletePayoffPlan(final PayoffPlan payoffPlan,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfPayoffPlan.handle(payoffPlan);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updatePayoffPlan(final PayoffPlan payoffPlan,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfPayoffPlan.handle(payoffPlan);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deactivateOtherPlans(final long activeId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeactivateOtherPlans.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, activeId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeactivateOtherPlans.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<PayoffPlan>> getAllActivePayoffPlans() {
    final String _sql = "SELECT * FROM payoff_plans WHERE isActive = 1 ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"payoff_plans"}, new Callable<List<PayoffPlan>>() {
      @Override
      @NonNull
      public List<PayoffPlan> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfStrategy = CursorUtil.getColumnIndexOrThrow(_cursor, "strategy");
          final int _cursorIndexOfTotalExtraPayment = CursorUtil.getColumnIndexOrThrow(_cursor, "totalExtraPayment");
          final int _cursorIndexOfTotalMonths = CursorUtil.getColumnIndexOrThrow(_cursor, "totalMonths");
          final int _cursorIndexOfTotalInterestSaved = CursorUtil.getColumnIndexOrThrow(_cursor, "totalInterestSaved");
          final int _cursorIndexOfTotalPayments = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPayments");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfTargetPayoffDate = CursorUtil.getColumnIndexOrThrow(_cursor, "targetPayoffDate");
          final int _cursorIndexOfMonthlyBudgetAllocation = CursorUtil.getColumnIndexOrThrow(_cursor, "monthlyBudgetAllocation");
          final List<PayoffPlan> _result = new ArrayList<PayoffPlan>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PayoffPlan _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpStrategy;
            if (_cursor.isNull(_cursorIndexOfStrategy)) {
              _tmpStrategy = null;
            } else {
              _tmpStrategy = _cursor.getString(_cursorIndexOfStrategy);
            }
            final double _tmpTotalExtraPayment;
            _tmpTotalExtraPayment = _cursor.getDouble(_cursorIndexOfTotalExtraPayment);
            final int _tmpTotalMonths;
            _tmpTotalMonths = _cursor.getInt(_cursorIndexOfTotalMonths);
            final double _tmpTotalInterestSaved;
            _tmpTotalInterestSaved = _cursor.getDouble(_cursorIndexOfTotalInterestSaved);
            final double _tmpTotalPayments;
            _tmpTotalPayments = _cursor.getDouble(_cursorIndexOfTotalPayments);
            final LocalDateTime _tmpCreatedAt;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = __converters.toLocalDateTime(_tmp);
            final boolean _tmpIsActive;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_1 != 0;
            final LocalDate _tmpTargetPayoffDate;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfTargetPayoffDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfTargetPayoffDate);
            }
            _tmpTargetPayoffDate = __converters.toLocalDate(_tmp_2);
            final Double _tmpMonthlyBudgetAllocation;
            if (_cursor.isNull(_cursorIndexOfMonthlyBudgetAllocation)) {
              _tmpMonthlyBudgetAllocation = null;
            } else {
              _tmpMonthlyBudgetAllocation = _cursor.getDouble(_cursorIndexOfMonthlyBudgetAllocation);
            }
            _item = new PayoffPlan(_tmpId,_tmpName,_tmpStrategy,_tmpTotalExtraPayment,_tmpTotalMonths,_tmpTotalInterestSaved,_tmpTotalPayments,_tmpCreatedAt,_tmpIsActive,_tmpTargetPayoffDate,_tmpMonthlyBudgetAllocation);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getPayoffPlanById(final long planId,
      final Continuation<? super PayoffPlan> $completion) {
    final String _sql = "SELECT * FROM payoff_plans WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, planId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<PayoffPlan>() {
      @Override
      @Nullable
      public PayoffPlan call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfStrategy = CursorUtil.getColumnIndexOrThrow(_cursor, "strategy");
          final int _cursorIndexOfTotalExtraPayment = CursorUtil.getColumnIndexOrThrow(_cursor, "totalExtraPayment");
          final int _cursorIndexOfTotalMonths = CursorUtil.getColumnIndexOrThrow(_cursor, "totalMonths");
          final int _cursorIndexOfTotalInterestSaved = CursorUtil.getColumnIndexOrThrow(_cursor, "totalInterestSaved");
          final int _cursorIndexOfTotalPayments = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPayments");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfTargetPayoffDate = CursorUtil.getColumnIndexOrThrow(_cursor, "targetPayoffDate");
          final int _cursorIndexOfMonthlyBudgetAllocation = CursorUtil.getColumnIndexOrThrow(_cursor, "monthlyBudgetAllocation");
          final PayoffPlan _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpStrategy;
            if (_cursor.isNull(_cursorIndexOfStrategy)) {
              _tmpStrategy = null;
            } else {
              _tmpStrategy = _cursor.getString(_cursorIndexOfStrategy);
            }
            final double _tmpTotalExtraPayment;
            _tmpTotalExtraPayment = _cursor.getDouble(_cursorIndexOfTotalExtraPayment);
            final int _tmpTotalMonths;
            _tmpTotalMonths = _cursor.getInt(_cursorIndexOfTotalMonths);
            final double _tmpTotalInterestSaved;
            _tmpTotalInterestSaved = _cursor.getDouble(_cursorIndexOfTotalInterestSaved);
            final double _tmpTotalPayments;
            _tmpTotalPayments = _cursor.getDouble(_cursorIndexOfTotalPayments);
            final LocalDateTime _tmpCreatedAt;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = __converters.toLocalDateTime(_tmp);
            final boolean _tmpIsActive;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_1 != 0;
            final LocalDate _tmpTargetPayoffDate;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfTargetPayoffDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfTargetPayoffDate);
            }
            _tmpTargetPayoffDate = __converters.toLocalDate(_tmp_2);
            final Double _tmpMonthlyBudgetAllocation;
            if (_cursor.isNull(_cursorIndexOfMonthlyBudgetAllocation)) {
              _tmpMonthlyBudgetAllocation = null;
            } else {
              _tmpMonthlyBudgetAllocation = _cursor.getDouble(_cursorIndexOfMonthlyBudgetAllocation);
            }
            _result = new PayoffPlan(_tmpId,_tmpName,_tmpStrategy,_tmpTotalExtraPayment,_tmpTotalMonths,_tmpTotalInterestSaved,_tmpTotalPayments,_tmpCreatedAt,_tmpIsActive,_tmpTargetPayoffDate,_tmpMonthlyBudgetAllocation);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCurrentActivePayoffPlan(final Continuation<? super PayoffPlan> $completion) {
    final String _sql = "SELECT * FROM payoff_plans WHERE isActive = 1 ORDER BY createdAt DESC LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<PayoffPlan>() {
      @Override
      @Nullable
      public PayoffPlan call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfStrategy = CursorUtil.getColumnIndexOrThrow(_cursor, "strategy");
          final int _cursorIndexOfTotalExtraPayment = CursorUtil.getColumnIndexOrThrow(_cursor, "totalExtraPayment");
          final int _cursorIndexOfTotalMonths = CursorUtil.getColumnIndexOrThrow(_cursor, "totalMonths");
          final int _cursorIndexOfTotalInterestSaved = CursorUtil.getColumnIndexOrThrow(_cursor, "totalInterestSaved");
          final int _cursorIndexOfTotalPayments = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPayments");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfTargetPayoffDate = CursorUtil.getColumnIndexOrThrow(_cursor, "targetPayoffDate");
          final int _cursorIndexOfMonthlyBudgetAllocation = CursorUtil.getColumnIndexOrThrow(_cursor, "monthlyBudgetAllocation");
          final PayoffPlan _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpStrategy;
            if (_cursor.isNull(_cursorIndexOfStrategy)) {
              _tmpStrategy = null;
            } else {
              _tmpStrategy = _cursor.getString(_cursorIndexOfStrategy);
            }
            final double _tmpTotalExtraPayment;
            _tmpTotalExtraPayment = _cursor.getDouble(_cursorIndexOfTotalExtraPayment);
            final int _tmpTotalMonths;
            _tmpTotalMonths = _cursor.getInt(_cursorIndexOfTotalMonths);
            final double _tmpTotalInterestSaved;
            _tmpTotalInterestSaved = _cursor.getDouble(_cursorIndexOfTotalInterestSaved);
            final double _tmpTotalPayments;
            _tmpTotalPayments = _cursor.getDouble(_cursorIndexOfTotalPayments);
            final LocalDateTime _tmpCreatedAt;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = __converters.toLocalDateTime(_tmp);
            final boolean _tmpIsActive;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_1 != 0;
            final LocalDate _tmpTargetPayoffDate;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfTargetPayoffDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfTargetPayoffDate);
            }
            _tmpTargetPayoffDate = __converters.toLocalDate(_tmp_2);
            final Double _tmpMonthlyBudgetAllocation;
            if (_cursor.isNull(_cursorIndexOfMonthlyBudgetAllocation)) {
              _tmpMonthlyBudgetAllocation = null;
            } else {
              _tmpMonthlyBudgetAllocation = _cursor.getDouble(_cursorIndexOfMonthlyBudgetAllocation);
            }
            _result = new PayoffPlan(_tmpId,_tmpName,_tmpStrategy,_tmpTotalExtraPayment,_tmpTotalMonths,_tmpTotalInterestSaved,_tmpTotalPayments,_tmpCreatedAt,_tmpIsActive,_tmpTargetPayoffDate,_tmpMonthlyBudgetAllocation);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<PayoffPlan> getCurrentActivePayoffPlanFlow() {
    final String _sql = "SELECT * FROM payoff_plans WHERE isActive = 1 ORDER BY createdAt DESC LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"payoff_plans"}, new Callable<PayoffPlan>() {
      @Override
      @Nullable
      public PayoffPlan call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfStrategy = CursorUtil.getColumnIndexOrThrow(_cursor, "strategy");
          final int _cursorIndexOfTotalExtraPayment = CursorUtil.getColumnIndexOrThrow(_cursor, "totalExtraPayment");
          final int _cursorIndexOfTotalMonths = CursorUtil.getColumnIndexOrThrow(_cursor, "totalMonths");
          final int _cursorIndexOfTotalInterestSaved = CursorUtil.getColumnIndexOrThrow(_cursor, "totalInterestSaved");
          final int _cursorIndexOfTotalPayments = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPayments");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfTargetPayoffDate = CursorUtil.getColumnIndexOrThrow(_cursor, "targetPayoffDate");
          final int _cursorIndexOfMonthlyBudgetAllocation = CursorUtil.getColumnIndexOrThrow(_cursor, "monthlyBudgetAllocation");
          final PayoffPlan _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpStrategy;
            if (_cursor.isNull(_cursorIndexOfStrategy)) {
              _tmpStrategy = null;
            } else {
              _tmpStrategy = _cursor.getString(_cursorIndexOfStrategy);
            }
            final double _tmpTotalExtraPayment;
            _tmpTotalExtraPayment = _cursor.getDouble(_cursorIndexOfTotalExtraPayment);
            final int _tmpTotalMonths;
            _tmpTotalMonths = _cursor.getInt(_cursorIndexOfTotalMonths);
            final double _tmpTotalInterestSaved;
            _tmpTotalInterestSaved = _cursor.getDouble(_cursorIndexOfTotalInterestSaved);
            final double _tmpTotalPayments;
            _tmpTotalPayments = _cursor.getDouble(_cursorIndexOfTotalPayments);
            final LocalDateTime _tmpCreatedAt;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = __converters.toLocalDateTime(_tmp);
            final boolean _tmpIsActive;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_1 != 0;
            final LocalDate _tmpTargetPayoffDate;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfTargetPayoffDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfTargetPayoffDate);
            }
            _tmpTargetPayoffDate = __converters.toLocalDate(_tmp_2);
            final Double _tmpMonthlyBudgetAllocation;
            if (_cursor.isNull(_cursorIndexOfMonthlyBudgetAllocation)) {
              _tmpMonthlyBudgetAllocation = null;
            } else {
              _tmpMonthlyBudgetAllocation = _cursor.getDouble(_cursorIndexOfMonthlyBudgetAllocation);
            }
            _result = new PayoffPlan(_tmpId,_tmpName,_tmpStrategy,_tmpTotalExtraPayment,_tmpTotalMonths,_tmpTotalInterestSaved,_tmpTotalPayments,_tmpCreatedAt,_tmpIsActive,_tmpTargetPayoffDate,_tmpMonthlyBudgetAllocation);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getActivePayoffPlanCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM payoff_plans WHERE isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<PayoffPlan>> getPayoffPlansByStrategy(final String strategy) {
    final String _sql = "SELECT * FROM payoff_plans WHERE strategy = ? AND isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (strategy == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, strategy);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"payoff_plans"}, new Callable<List<PayoffPlan>>() {
      @Override
      @NonNull
      public List<PayoffPlan> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfStrategy = CursorUtil.getColumnIndexOrThrow(_cursor, "strategy");
          final int _cursorIndexOfTotalExtraPayment = CursorUtil.getColumnIndexOrThrow(_cursor, "totalExtraPayment");
          final int _cursorIndexOfTotalMonths = CursorUtil.getColumnIndexOrThrow(_cursor, "totalMonths");
          final int _cursorIndexOfTotalInterestSaved = CursorUtil.getColumnIndexOrThrow(_cursor, "totalInterestSaved");
          final int _cursorIndexOfTotalPayments = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPayments");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfTargetPayoffDate = CursorUtil.getColumnIndexOrThrow(_cursor, "targetPayoffDate");
          final int _cursorIndexOfMonthlyBudgetAllocation = CursorUtil.getColumnIndexOrThrow(_cursor, "monthlyBudgetAllocation");
          final List<PayoffPlan> _result = new ArrayList<PayoffPlan>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PayoffPlan _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpStrategy;
            if (_cursor.isNull(_cursorIndexOfStrategy)) {
              _tmpStrategy = null;
            } else {
              _tmpStrategy = _cursor.getString(_cursorIndexOfStrategy);
            }
            final double _tmpTotalExtraPayment;
            _tmpTotalExtraPayment = _cursor.getDouble(_cursorIndexOfTotalExtraPayment);
            final int _tmpTotalMonths;
            _tmpTotalMonths = _cursor.getInt(_cursorIndexOfTotalMonths);
            final double _tmpTotalInterestSaved;
            _tmpTotalInterestSaved = _cursor.getDouble(_cursorIndexOfTotalInterestSaved);
            final double _tmpTotalPayments;
            _tmpTotalPayments = _cursor.getDouble(_cursorIndexOfTotalPayments);
            final LocalDateTime _tmpCreatedAt;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = __converters.toLocalDateTime(_tmp);
            final boolean _tmpIsActive;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_1 != 0;
            final LocalDate _tmpTargetPayoffDate;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfTargetPayoffDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfTargetPayoffDate);
            }
            _tmpTargetPayoffDate = __converters.toLocalDate(_tmp_2);
            final Double _tmpMonthlyBudgetAllocation;
            if (_cursor.isNull(_cursorIndexOfMonthlyBudgetAllocation)) {
              _tmpMonthlyBudgetAllocation = null;
            } else {
              _tmpMonthlyBudgetAllocation = _cursor.getDouble(_cursorIndexOfMonthlyBudgetAllocation);
            }
            _item = new PayoffPlan(_tmpId,_tmpName,_tmpStrategy,_tmpTotalExtraPayment,_tmpTotalMonths,_tmpTotalInterestSaved,_tmpTotalPayments,_tmpCreatedAt,_tmpIsActive,_tmpTargetPayoffDate,_tmpMonthlyBudgetAllocation);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
