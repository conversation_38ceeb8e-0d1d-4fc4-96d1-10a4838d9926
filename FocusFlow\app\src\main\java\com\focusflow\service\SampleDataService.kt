package com.focusflow.service

import com.focusflow.data.model.BudgetCategory
import com.focusflow.data.model.CreditCard
import com.focusflow.data.model.UserPreferences
import com.focusflow.data.repository.BudgetCategoryRepository
import com.focusflow.data.repository.CreditCardRepository
import com.focusflow.data.repository.UserPreferencesRepository
import kotlinx.datetime.Clock
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SampleDataService @Inject constructor(
    private val creditCardRepository: CreditCardRepository,
    private val budgetCategoryRepository: BudgetCategoryRepository,
    private val userPreferencesRepository: UserPreferencesRepository
) {
    
    suspend fun initializeSampleData() {
        // Check if sample data already exists
        val existingCards = creditCardRepository.getAllCreditCardsSync()
        if (existingCards.isNotEmpty()) {
            return // Sample data already exists
        }

        // Create sample credit cards for testing
        val sampleCards = listOf(
            CreditCard(
                name = "High Interest Card",
                currentBalance = 5000.0,
                creditLimit = 10000.0,
                minimumPayment = 150.0,
                dueDate = LocalDate(2024, 1, 15),
                interestRate = 24.99
            ),
            CreditCard(
                name = "Low Balance Card",
                currentBalance = 1500.0,
                creditLimit = 5000.0,
                minimumPayment = 50.0,
                dueDate = LocalDate(2024, 1, 20),
                interestRate = 18.99
            ),
            CreditCard(
                name = "Medium Card",
                currentBalance = 3000.0,
                creditLimit = 8000.0,
                minimumPayment = 100.0,
                dueDate = LocalDate(2024, 1, 25),
                interestRate = 21.99
            )
        )

        sampleCards.forEach { card ->
            creditCardRepository.insertCreditCard(card)
        }

        // Create sample budget categories
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        val sampleCategories = listOf(
            BudgetCategory(
                name = "Groceries",
                allocatedAmount = 300.0,
                spentAmount = 200.0,
                budgetPeriod = "weekly",
                budgetYear = now.year,
                budgetWeek = now.dayOfYear / 7 + 1,
                categoryColor = "#4CAF50"
            ),
            BudgetCategory(
                name = "Entertainment",
                allocatedAmount = 150.0,
                spentAmount = 50.0,
                budgetPeriod = "weekly",
                budgetYear = now.year,
                budgetWeek = now.dayOfYear / 7 + 1,
                categoryColor = "#FF9800"
            ),
            BudgetCategory(
                name = "Transportation",
                allocatedAmount = 200.0,
                spentAmount = 75.0,
                budgetPeriod = "weekly",
                budgetYear = now.year,
                budgetWeek = now.dayOfYear / 7 + 1,
                categoryColor = "#2196F3"
            ),
            BudgetCategory(
                name = "Dining Out",
                allocatedAmount = 100.0,
                spentAmount = 80.0,
                budgetPeriod = "weekly",
                budgetYear = now.year,
                budgetWeek = now.dayOfYear / 7 + 1,
                categoryColor = "#E91E63"
            )
        )

        sampleCategories.forEach { category ->
            budgetCategoryRepository.insertBudgetCategory(category)
        }

        // Create sample user preferences with impulse control enabled
        val existingPrefs = userPreferencesRepository.getUserPreferencesSync()
        if (existingPrefs == null) {
            val samplePrefs = UserPreferences(
                id = 1,
                hasCompletedOnboarding = true,
                budgetPeriod = "weekly",
                weeklyIncome = 1000.0,
                impulseControlEnabled = true,
                spendingThreshold = 50.0,
                useZeroBasedBudgeting = true,
                envelopeViewEnabled = true,
                notificationsEnabled = true
            )
            userPreferencesRepository.insertUserPreferences(samplePrefs)
        }
    }
}
