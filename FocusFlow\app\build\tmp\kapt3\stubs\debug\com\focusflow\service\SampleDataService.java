package com.focusflow.service;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u000e\u0010\t\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u000bR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/focusflow/service/SampleDataService;", "", "creditCardRepository", "Lcom/focusflow/data/repository/CreditCardRepository;", "budgetCategoryRepository", "Lcom/focusflow/data/repository/BudgetCategoryRepository;", "userPreferencesRepository", "Lcom/focusflow/data/repository/UserPreferencesRepository;", "(Lcom/focusflow/data/repository/CreditCardRepository;Lcom/focusflow/data/repository/BudgetCategoryRepository;Lcom/focusflow/data/repository/UserPreferencesRepository;)V", "initializeSampleData", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class SampleDataService {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.CreditCardRepository creditCardRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.BudgetCategoryRepository budgetCategoryRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository = null;
    
    @javax.inject.Inject
    public SampleDataService(@org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.CreditCardRepository creditCardRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.BudgetCategoryRepository budgetCategoryRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object initializeSampleData(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}