package com.focusflow.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000@\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\u001a*\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\b\u0010\t\u001a,\u0010\n\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\f2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u000e2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00010\u000eH\u0007\u001a \u0010\u0010\u001a\u00020\u00012\u0006\u0010\u0011\u001a\u00020\u00052\u0006\u0010\u0012\u001a\u00020\u00052\u0006\u0010\u0013\u001a\u00020\u0003H\u0007\u001a\u0012\u0010\u0014\u001a\u00020\u00012\b\b\u0002\u0010\u0015\u001a\u00020\u0016H\u0007\u001a\b\u0010\u0017\u001a\u00020\u0001H\u0007\u001a<\u0010\u0018\u001a\u00020\u00012\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00010\u000e2$\u0010\u001a\u001a \u0012\u0016\u0012\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00050\u001d0\u001c\u0012\u0004\u0012\u00020\u00010\u001bH\u0007\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u001e"}, d2 = {"BudgetAmountItem", "", "label", "", "amount", "", "color", "Landroidx/compose/ui/graphics/Color;", "BudgetAmountItem-mxwnekA", "(Ljava/lang/String;DJ)V", "BudgetCategoryItem", "budgetCategory", "Lcom/focusflow/data/model/BudgetCategory;", "onEdit", "Lkotlin/Function0;", "onDelete", "BudgetOverviewCard", "totalBudget", "totalSpent", "period", "BudgetScreen", "viewModel", "Lcom/focusflow/ui/viewmodel/BudgetViewModel;", "EmptyBudgetState", "QuickSetupDialog", "onDismiss", "onSetupComplete", "Lkotlin/Function1;", "", "Lkotlin/Pair;", "app_debug"})
public final class PlaceholderScreensKt {
    
    @androidx.compose.runtime.Composable
    public static final void BudgetScreen(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.BudgetViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void BudgetOverviewCard(double totalBudget, double totalSpent, @org.jetbrains.annotations.NotNull
    java.lang.String period) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void BudgetCategoryItem(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetCategory budgetCategory, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onEdit, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDelete) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void EmptyBudgetState() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void QuickSetupDialog(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.util.List<kotlin.Pair<java.lang.String, java.lang.Double>>, kotlin.Unit> onSetupComplete) {
    }
}