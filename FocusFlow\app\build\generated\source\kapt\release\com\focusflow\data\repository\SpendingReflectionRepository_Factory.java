package com.focusflow.data.repository;

import com.focusflow.data.dao.SpendingReflectionDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SpendingReflectionRepository_Factory implements Factory<SpendingReflectionRepository> {
  private final Provider<SpendingReflectionDao> spendingReflectionDaoProvider;

  public SpendingReflectionRepository_Factory(
      Provider<SpendingReflectionDao> spendingReflectionDaoProvider) {
    this.spendingReflectionDaoProvider = spendingReflectionDaoProvider;
  }

  @Override
  public SpendingReflectionRepository get() {
    return newInstance(spendingReflectionDaoProvider.get());
  }

  public static SpendingReflectionRepository_Factory create(
      Provider<SpendingReflectionDao> spendingReflectionDaoProvider) {
    return new SpendingReflectionRepository_Factory(spendingReflectionDaoProvider);
  }

  public static SpendingReflectionRepository newInstance(
      SpendingReflectionDao spendingReflectionDao) {
    return new SpendingReflectionRepository(spendingReflectionDao);
  }
}
