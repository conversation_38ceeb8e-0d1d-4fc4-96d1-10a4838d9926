package com.focusflow.data.repository;

import com.focusflow.data.dao.BudgetCategoryDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class BudgetCategoryRepository_Factory implements Factory<BudgetCategoryRepository> {
  private final Provider<BudgetCategoryDao> budgetCategoryDaoProvider;

  public BudgetCategoryRepository_Factory(Provider<BudgetCategoryDao> budgetCategoryDaoProvider) {
    this.budgetCategoryDaoProvider = budgetCategoryDaoProvider;
  }

  @Override
  public BudgetCategoryRepository get() {
    return newInstance(budgetCategoryDaoProvider.get());
  }

  public static BudgetCategoryRepository_Factory create(
      Provider<BudgetCategoryDao> budgetCategoryDaoProvider) {
    return new BudgetCategoryRepository_Factory(budgetCategoryDaoProvider);
  }

  public static BudgetCategoryRepository newInstance(BudgetCategoryDao budgetCategoryDao) {
    return new BudgetCategoryRepository(budgetCategoryDao);
  }
}
