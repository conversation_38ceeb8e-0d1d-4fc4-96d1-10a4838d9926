{"logs": [{"outputFile": "com.focusflow.app-mergeReleaseResources-66:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1f170a70d96fcd26f910cf5fef71c6ab\\transformed\\jetified-ui-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,990,1061,1141,1226,1299,1378,1448", "endColumns": "96,86,96,100,85,76,90,91,84,71,70,79,84,72,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,985,1056,1136,1221,1294,1373,1443,1561"}, "to": {"startLines": "36,37,39,41,42,53,54,55,56,57,58,59,60,62,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3562,3659,3857,4047,4148,5511,5588,5679,5771,5856,5928,5999,6079,6251,6425,6504,6574", "endColumns": "96,86,96,100,85,76,90,91,84,71,70,79,84,72,78,69,117", "endOffsets": "3654,3741,3949,4143,4229,5583,5674,5766,5851,5923,5994,6074,6159,6319,6499,6569,6687"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\904fd36595e0135e8fdca5c15906c24d\\transformed\\core-1.12.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2836,2934,3036,3133,3237,3341,3446,6324", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "2929,3031,3128,3232,3336,3441,3557,6420"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0e1dc919567705f737931e90f7aead7b\\transformed\\biometric-1.1.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,259,380,514,645,772,903,1038,1138,1277,1410", "endColumns": "110,92,120,133,130,126,130,134,99,138,132,125", "endOffsets": "161,254,375,509,640,767,898,1033,1133,1272,1405,1531"}, "to": {"startLines": "38,40,43,44,45,46,47,48,49,50,51,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3746,3954,4234,4355,4489,4620,4747,4878,5013,5113,5252,5385", "endColumns": "110,92,120,133,130,126,130,134,99,138,132,125", "endOffsets": "3852,4042,4350,4484,4615,4742,4873,5008,5108,5247,5380,5506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b7575953e232ac886e0021593ed04f20\\transformed\\appcompat-1.6.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,6164", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,6246"}}]}]}