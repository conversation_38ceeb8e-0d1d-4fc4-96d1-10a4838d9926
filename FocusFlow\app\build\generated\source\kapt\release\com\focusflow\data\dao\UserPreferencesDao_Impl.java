package com.focusflow.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.focusflow.data.model.UserPreferences;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class UserPreferencesDao_Impl implements UserPreferencesDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<UserPreferences> __insertionAdapterOfUserPreferences;

  private final EntityDeletionOrUpdateAdapter<UserPreferences> __updateAdapterOfUserPreferences;

  private final SharedSQLiteStatement __preparedStmtOfUpdateBudgetPeriod;

  private final SharedSQLiteStatement __preparedStmtOfUpdateNotificationsEnabled;

  private final SharedSQLiteStatement __preparedStmtOfUpdateDarkModeEnabled;

  private final SharedSQLiteStatement __preparedStmtOfUpdateFontSize;

  private final SharedSQLiteStatement __preparedStmtOfUpdateThemePreference;

  private final SharedSQLiteStatement __preparedStmtOfUpdateFontScale;

  private final SharedSQLiteStatement __preparedStmtOfUpdateHighContrastMode;

  private final SharedSQLiteStatement __preparedStmtOfUpdateVoiceInputEnabled;

  private final SharedSQLiteStatement __preparedStmtOfUpdateAnimationsEnabled;

  public UserPreferencesDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfUserPreferences = new EntityInsertionAdapter<UserPreferences>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `user_preferences` (`id`,`budgetPeriod`,`notificationsEnabled`,`reminderTime`,`darkModeEnabled`,`fontSize`,`primaryGoal`,`weeklyBudget`,`monthlyBudget`,`hasCompletedOnboarding`,`enableNotifications`,`notificationTime`,`theme`,`themePreference`,`fontScale`,`highContrastMode`,`voiceInputEnabled`,`animationsEnabled`,`weeklyIncome`,`monthlyIncome`,`useZeroBasedBudgeting`,`envelopeViewEnabled`,`impulseControlEnabled`,`spendingThreshold`,`coolingOffPeriodSeconds`,`enableReflectionQuestions`,`enableBudgetWarnings`,`enableWishlistSuggestions`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserPreferences entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getBudgetPeriod() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getBudgetPeriod());
        }
        final int _tmp = entity.getNotificationsEnabled() ? 1 : 0;
        statement.bindLong(3, _tmp);
        if (entity.getReminderTime() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getReminderTime());
        }
        final int _tmp_1 = entity.getDarkModeEnabled() ? 1 : 0;
        statement.bindLong(5, _tmp_1);
        if (entity.getFontSize() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getFontSize());
        }
        if (entity.getPrimaryGoal() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getPrimaryGoal());
        }
        if (entity.getWeeklyBudget() == null) {
          statement.bindNull(8);
        } else {
          statement.bindDouble(8, entity.getWeeklyBudget());
        }
        if (entity.getMonthlyBudget() == null) {
          statement.bindNull(9);
        } else {
          statement.bindDouble(9, entity.getMonthlyBudget());
        }
        final int _tmp_2 = entity.getHasCompletedOnboarding() ? 1 : 0;
        statement.bindLong(10, _tmp_2);
        final int _tmp_3 = entity.getEnableNotifications() ? 1 : 0;
        statement.bindLong(11, _tmp_3);
        if (entity.getNotificationTime() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getNotificationTime());
        }
        if (entity.getTheme() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getTheme());
        }
        if (entity.getThemePreference() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getThemePreference());
        }
        statement.bindDouble(15, entity.getFontScale());
        final int _tmp_4 = entity.getHighContrastMode() ? 1 : 0;
        statement.bindLong(16, _tmp_4);
        final int _tmp_5 = entity.getVoiceInputEnabled() ? 1 : 0;
        statement.bindLong(17, _tmp_5);
        final int _tmp_6 = entity.getAnimationsEnabled() ? 1 : 0;
        statement.bindLong(18, _tmp_6);
        if (entity.getWeeklyIncome() == null) {
          statement.bindNull(19);
        } else {
          statement.bindDouble(19, entity.getWeeklyIncome());
        }
        if (entity.getMonthlyIncome() == null) {
          statement.bindNull(20);
        } else {
          statement.bindDouble(20, entity.getMonthlyIncome());
        }
        final int _tmp_7 = entity.getUseZeroBasedBudgeting() ? 1 : 0;
        statement.bindLong(21, _tmp_7);
        final int _tmp_8 = entity.getEnvelopeViewEnabled() ? 1 : 0;
        statement.bindLong(22, _tmp_8);
        final int _tmp_9 = entity.getImpulseControlEnabled() ? 1 : 0;
        statement.bindLong(23, _tmp_9);
        statement.bindDouble(24, entity.getSpendingThreshold());
        statement.bindLong(25, entity.getCoolingOffPeriodSeconds());
        final int _tmp_10 = entity.getEnableReflectionQuestions() ? 1 : 0;
        statement.bindLong(26, _tmp_10);
        final int _tmp_11 = entity.getEnableBudgetWarnings() ? 1 : 0;
        statement.bindLong(27, _tmp_11);
        final int _tmp_12 = entity.getEnableWishlistSuggestions() ? 1 : 0;
        statement.bindLong(28, _tmp_12);
      }
    };
    this.__updateAdapterOfUserPreferences = new EntityDeletionOrUpdateAdapter<UserPreferences>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `user_preferences` SET `id` = ?,`budgetPeriod` = ?,`notificationsEnabled` = ?,`reminderTime` = ?,`darkModeEnabled` = ?,`fontSize` = ?,`primaryGoal` = ?,`weeklyBudget` = ?,`monthlyBudget` = ?,`hasCompletedOnboarding` = ?,`enableNotifications` = ?,`notificationTime` = ?,`theme` = ?,`themePreference` = ?,`fontScale` = ?,`highContrastMode` = ?,`voiceInputEnabled` = ?,`animationsEnabled` = ?,`weeklyIncome` = ?,`monthlyIncome` = ?,`useZeroBasedBudgeting` = ?,`envelopeViewEnabled` = ?,`impulseControlEnabled` = ?,`spendingThreshold` = ?,`coolingOffPeriodSeconds` = ?,`enableReflectionQuestions` = ?,`enableBudgetWarnings` = ?,`enableWishlistSuggestions` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserPreferences entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getBudgetPeriod() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getBudgetPeriod());
        }
        final int _tmp = entity.getNotificationsEnabled() ? 1 : 0;
        statement.bindLong(3, _tmp);
        if (entity.getReminderTime() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getReminderTime());
        }
        final int _tmp_1 = entity.getDarkModeEnabled() ? 1 : 0;
        statement.bindLong(5, _tmp_1);
        if (entity.getFontSize() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getFontSize());
        }
        if (entity.getPrimaryGoal() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getPrimaryGoal());
        }
        if (entity.getWeeklyBudget() == null) {
          statement.bindNull(8);
        } else {
          statement.bindDouble(8, entity.getWeeklyBudget());
        }
        if (entity.getMonthlyBudget() == null) {
          statement.bindNull(9);
        } else {
          statement.bindDouble(9, entity.getMonthlyBudget());
        }
        final int _tmp_2 = entity.getHasCompletedOnboarding() ? 1 : 0;
        statement.bindLong(10, _tmp_2);
        final int _tmp_3 = entity.getEnableNotifications() ? 1 : 0;
        statement.bindLong(11, _tmp_3);
        if (entity.getNotificationTime() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getNotificationTime());
        }
        if (entity.getTheme() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getTheme());
        }
        if (entity.getThemePreference() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getThemePreference());
        }
        statement.bindDouble(15, entity.getFontScale());
        final int _tmp_4 = entity.getHighContrastMode() ? 1 : 0;
        statement.bindLong(16, _tmp_4);
        final int _tmp_5 = entity.getVoiceInputEnabled() ? 1 : 0;
        statement.bindLong(17, _tmp_5);
        final int _tmp_6 = entity.getAnimationsEnabled() ? 1 : 0;
        statement.bindLong(18, _tmp_6);
        if (entity.getWeeklyIncome() == null) {
          statement.bindNull(19);
        } else {
          statement.bindDouble(19, entity.getWeeklyIncome());
        }
        if (entity.getMonthlyIncome() == null) {
          statement.bindNull(20);
        } else {
          statement.bindDouble(20, entity.getMonthlyIncome());
        }
        final int _tmp_7 = entity.getUseZeroBasedBudgeting() ? 1 : 0;
        statement.bindLong(21, _tmp_7);
        final int _tmp_8 = entity.getEnvelopeViewEnabled() ? 1 : 0;
        statement.bindLong(22, _tmp_8);
        final int _tmp_9 = entity.getImpulseControlEnabled() ? 1 : 0;
        statement.bindLong(23, _tmp_9);
        statement.bindDouble(24, entity.getSpendingThreshold());
        statement.bindLong(25, entity.getCoolingOffPeriodSeconds());
        final int _tmp_10 = entity.getEnableReflectionQuestions() ? 1 : 0;
        statement.bindLong(26, _tmp_10);
        final int _tmp_11 = entity.getEnableBudgetWarnings() ? 1 : 0;
        statement.bindLong(27, _tmp_11);
        final int _tmp_12 = entity.getEnableWishlistSuggestions() ? 1 : 0;
        statement.bindLong(28, _tmp_12);
        statement.bindLong(29, entity.getId());
      }
    };
    this.__preparedStmtOfUpdateBudgetPeriod = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_preferences SET budgetPeriod = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateNotificationsEnabled = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_preferences SET notificationsEnabled = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateDarkModeEnabled = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_preferences SET darkModeEnabled = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateFontSize = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_preferences SET fontSize = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateThemePreference = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_preferences SET themePreference = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateFontScale = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_preferences SET fontScale = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateHighContrastMode = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_preferences SET highContrastMode = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateVoiceInputEnabled = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_preferences SET voiceInputEnabled = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateAnimationsEnabled = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_preferences SET animationsEnabled = ? WHERE id = 1";
        return _query;
      }
    };
  }

  @Override
  public Object insertUserPreferences(final UserPreferences preferences,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfUserPreferences.insert(preferences);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateUserPreferences(final UserPreferences preferences,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfUserPreferences.handle(preferences);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateBudgetPeriod(final String period,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateBudgetPeriod.acquire();
        int _argIndex = 1;
        if (period == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, period);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateBudgetPeriod.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateNotificationsEnabled(final boolean enabled,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateNotificationsEnabled.acquire();
        int _argIndex = 1;
        final int _tmp = enabled ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateNotificationsEnabled.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateDarkModeEnabled(final boolean enabled,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateDarkModeEnabled.acquire();
        int _argIndex = 1;
        final int _tmp = enabled ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateDarkModeEnabled.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateFontSize(final String fontSize,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateFontSize.acquire();
        int _argIndex = 1;
        if (fontSize == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, fontSize);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateFontSize.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateThemePreference(final String themePreference,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateThemePreference.acquire();
        int _argIndex = 1;
        if (themePreference == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, themePreference);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateThemePreference.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateFontScale(final float fontScale,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateFontScale.acquire();
        int _argIndex = 1;
        _stmt.bindDouble(_argIndex, fontScale);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateFontScale.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateHighContrastMode(final boolean enabled,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateHighContrastMode.acquire();
        int _argIndex = 1;
        final int _tmp = enabled ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateHighContrastMode.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateVoiceInputEnabled(final boolean enabled,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateVoiceInputEnabled.acquire();
        int _argIndex = 1;
        final int _tmp = enabled ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateVoiceInputEnabled.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateAnimationsEnabled(final boolean enabled,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateAnimationsEnabled.acquire();
        int _argIndex = 1;
        final int _tmp = enabled ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateAnimationsEnabled.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<UserPreferences> getUserPreferences() {
    final String _sql = "SELECT * FROM user_preferences WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"user_preferences"}, new Callable<UserPreferences>() {
      @Override
      @Nullable
      public UserPreferences call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetPeriod");
          final int _cursorIndexOfNotificationsEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "notificationsEnabled");
          final int _cursorIndexOfReminderTime = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderTime");
          final int _cursorIndexOfDarkModeEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "darkModeEnabled");
          final int _cursorIndexOfFontSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fontSize");
          final int _cursorIndexOfPrimaryGoal = CursorUtil.getColumnIndexOrThrow(_cursor, "primaryGoal");
          final int _cursorIndexOfWeeklyBudget = CursorUtil.getColumnIndexOrThrow(_cursor, "weeklyBudget");
          final int _cursorIndexOfMonthlyBudget = CursorUtil.getColumnIndexOrThrow(_cursor, "monthlyBudget");
          final int _cursorIndexOfHasCompletedOnboarding = CursorUtil.getColumnIndexOrThrow(_cursor, "hasCompletedOnboarding");
          final int _cursorIndexOfEnableNotifications = CursorUtil.getColumnIndexOrThrow(_cursor, "enableNotifications");
          final int _cursorIndexOfNotificationTime = CursorUtil.getColumnIndexOrThrow(_cursor, "notificationTime");
          final int _cursorIndexOfTheme = CursorUtil.getColumnIndexOrThrow(_cursor, "theme");
          final int _cursorIndexOfThemePreference = CursorUtil.getColumnIndexOrThrow(_cursor, "themePreference");
          final int _cursorIndexOfFontScale = CursorUtil.getColumnIndexOrThrow(_cursor, "fontScale");
          final int _cursorIndexOfHighContrastMode = CursorUtil.getColumnIndexOrThrow(_cursor, "highContrastMode");
          final int _cursorIndexOfVoiceInputEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "voiceInputEnabled");
          final int _cursorIndexOfAnimationsEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "animationsEnabled");
          final int _cursorIndexOfWeeklyIncome = CursorUtil.getColumnIndexOrThrow(_cursor, "weeklyIncome");
          final int _cursorIndexOfMonthlyIncome = CursorUtil.getColumnIndexOrThrow(_cursor, "monthlyIncome");
          final int _cursorIndexOfUseZeroBasedBudgeting = CursorUtil.getColumnIndexOrThrow(_cursor, "useZeroBasedBudgeting");
          final int _cursorIndexOfEnvelopeViewEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "envelopeViewEnabled");
          final int _cursorIndexOfImpulseControlEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "impulseControlEnabled");
          final int _cursorIndexOfSpendingThreshold = CursorUtil.getColumnIndexOrThrow(_cursor, "spendingThreshold");
          final int _cursorIndexOfCoolingOffPeriodSeconds = CursorUtil.getColumnIndexOrThrow(_cursor, "coolingOffPeriodSeconds");
          final int _cursorIndexOfEnableReflectionQuestions = CursorUtil.getColumnIndexOrThrow(_cursor, "enableReflectionQuestions");
          final int _cursorIndexOfEnableBudgetWarnings = CursorUtil.getColumnIndexOrThrow(_cursor, "enableBudgetWarnings");
          final int _cursorIndexOfEnableWishlistSuggestions = CursorUtil.getColumnIndexOrThrow(_cursor, "enableWishlistSuggestions");
          final UserPreferences _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            final boolean _tmpNotificationsEnabled;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfNotificationsEnabled);
            _tmpNotificationsEnabled = _tmp != 0;
            final String _tmpReminderTime;
            if (_cursor.isNull(_cursorIndexOfReminderTime)) {
              _tmpReminderTime = null;
            } else {
              _tmpReminderTime = _cursor.getString(_cursorIndexOfReminderTime);
            }
            final boolean _tmpDarkModeEnabled;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfDarkModeEnabled);
            _tmpDarkModeEnabled = _tmp_1 != 0;
            final String _tmpFontSize;
            if (_cursor.isNull(_cursorIndexOfFontSize)) {
              _tmpFontSize = null;
            } else {
              _tmpFontSize = _cursor.getString(_cursorIndexOfFontSize);
            }
            final String _tmpPrimaryGoal;
            if (_cursor.isNull(_cursorIndexOfPrimaryGoal)) {
              _tmpPrimaryGoal = null;
            } else {
              _tmpPrimaryGoal = _cursor.getString(_cursorIndexOfPrimaryGoal);
            }
            final Double _tmpWeeklyBudget;
            if (_cursor.isNull(_cursorIndexOfWeeklyBudget)) {
              _tmpWeeklyBudget = null;
            } else {
              _tmpWeeklyBudget = _cursor.getDouble(_cursorIndexOfWeeklyBudget);
            }
            final Double _tmpMonthlyBudget;
            if (_cursor.isNull(_cursorIndexOfMonthlyBudget)) {
              _tmpMonthlyBudget = null;
            } else {
              _tmpMonthlyBudget = _cursor.getDouble(_cursorIndexOfMonthlyBudget);
            }
            final boolean _tmpHasCompletedOnboarding;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfHasCompletedOnboarding);
            _tmpHasCompletedOnboarding = _tmp_2 != 0;
            final boolean _tmpEnableNotifications;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfEnableNotifications);
            _tmpEnableNotifications = _tmp_3 != 0;
            final String _tmpNotificationTime;
            if (_cursor.isNull(_cursorIndexOfNotificationTime)) {
              _tmpNotificationTime = null;
            } else {
              _tmpNotificationTime = _cursor.getString(_cursorIndexOfNotificationTime);
            }
            final String _tmpTheme;
            if (_cursor.isNull(_cursorIndexOfTheme)) {
              _tmpTheme = null;
            } else {
              _tmpTheme = _cursor.getString(_cursorIndexOfTheme);
            }
            final String _tmpThemePreference;
            if (_cursor.isNull(_cursorIndexOfThemePreference)) {
              _tmpThemePreference = null;
            } else {
              _tmpThemePreference = _cursor.getString(_cursorIndexOfThemePreference);
            }
            final float _tmpFontScale;
            _tmpFontScale = _cursor.getFloat(_cursorIndexOfFontScale);
            final boolean _tmpHighContrastMode;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfHighContrastMode);
            _tmpHighContrastMode = _tmp_4 != 0;
            final boolean _tmpVoiceInputEnabled;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfVoiceInputEnabled);
            _tmpVoiceInputEnabled = _tmp_5 != 0;
            final boolean _tmpAnimationsEnabled;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfAnimationsEnabled);
            _tmpAnimationsEnabled = _tmp_6 != 0;
            final Double _tmpWeeklyIncome;
            if (_cursor.isNull(_cursorIndexOfWeeklyIncome)) {
              _tmpWeeklyIncome = null;
            } else {
              _tmpWeeklyIncome = _cursor.getDouble(_cursorIndexOfWeeklyIncome);
            }
            final Double _tmpMonthlyIncome;
            if (_cursor.isNull(_cursorIndexOfMonthlyIncome)) {
              _tmpMonthlyIncome = null;
            } else {
              _tmpMonthlyIncome = _cursor.getDouble(_cursorIndexOfMonthlyIncome);
            }
            final boolean _tmpUseZeroBasedBudgeting;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfUseZeroBasedBudgeting);
            _tmpUseZeroBasedBudgeting = _tmp_7 != 0;
            final boolean _tmpEnvelopeViewEnabled;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfEnvelopeViewEnabled);
            _tmpEnvelopeViewEnabled = _tmp_8 != 0;
            final boolean _tmpImpulseControlEnabled;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfImpulseControlEnabled);
            _tmpImpulseControlEnabled = _tmp_9 != 0;
            final double _tmpSpendingThreshold;
            _tmpSpendingThreshold = _cursor.getDouble(_cursorIndexOfSpendingThreshold);
            final int _tmpCoolingOffPeriodSeconds;
            _tmpCoolingOffPeriodSeconds = _cursor.getInt(_cursorIndexOfCoolingOffPeriodSeconds);
            final boolean _tmpEnableReflectionQuestions;
            final int _tmp_10;
            _tmp_10 = _cursor.getInt(_cursorIndexOfEnableReflectionQuestions);
            _tmpEnableReflectionQuestions = _tmp_10 != 0;
            final boolean _tmpEnableBudgetWarnings;
            final int _tmp_11;
            _tmp_11 = _cursor.getInt(_cursorIndexOfEnableBudgetWarnings);
            _tmpEnableBudgetWarnings = _tmp_11 != 0;
            final boolean _tmpEnableWishlistSuggestions;
            final int _tmp_12;
            _tmp_12 = _cursor.getInt(_cursorIndexOfEnableWishlistSuggestions);
            _tmpEnableWishlistSuggestions = _tmp_12 != 0;
            _result = new UserPreferences(_tmpId,_tmpBudgetPeriod,_tmpNotificationsEnabled,_tmpReminderTime,_tmpDarkModeEnabled,_tmpFontSize,_tmpPrimaryGoal,_tmpWeeklyBudget,_tmpMonthlyBudget,_tmpHasCompletedOnboarding,_tmpEnableNotifications,_tmpNotificationTime,_tmpTheme,_tmpThemePreference,_tmpFontScale,_tmpHighContrastMode,_tmpVoiceInputEnabled,_tmpAnimationsEnabled,_tmpWeeklyIncome,_tmpMonthlyIncome,_tmpUseZeroBasedBudgeting,_tmpEnvelopeViewEnabled,_tmpImpulseControlEnabled,_tmpSpendingThreshold,_tmpCoolingOffPeriodSeconds,_tmpEnableReflectionQuestions,_tmpEnableBudgetWarnings,_tmpEnableWishlistSuggestions);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getUserPreferencesSync(final Continuation<? super UserPreferences> $completion) {
    final String _sql = "SELECT * FROM user_preferences WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<UserPreferences>() {
      @Override
      @Nullable
      public UserPreferences call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfBudgetPeriod = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetPeriod");
          final int _cursorIndexOfNotificationsEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "notificationsEnabled");
          final int _cursorIndexOfReminderTime = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderTime");
          final int _cursorIndexOfDarkModeEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "darkModeEnabled");
          final int _cursorIndexOfFontSize = CursorUtil.getColumnIndexOrThrow(_cursor, "fontSize");
          final int _cursorIndexOfPrimaryGoal = CursorUtil.getColumnIndexOrThrow(_cursor, "primaryGoal");
          final int _cursorIndexOfWeeklyBudget = CursorUtil.getColumnIndexOrThrow(_cursor, "weeklyBudget");
          final int _cursorIndexOfMonthlyBudget = CursorUtil.getColumnIndexOrThrow(_cursor, "monthlyBudget");
          final int _cursorIndexOfHasCompletedOnboarding = CursorUtil.getColumnIndexOrThrow(_cursor, "hasCompletedOnboarding");
          final int _cursorIndexOfEnableNotifications = CursorUtil.getColumnIndexOrThrow(_cursor, "enableNotifications");
          final int _cursorIndexOfNotificationTime = CursorUtil.getColumnIndexOrThrow(_cursor, "notificationTime");
          final int _cursorIndexOfTheme = CursorUtil.getColumnIndexOrThrow(_cursor, "theme");
          final int _cursorIndexOfThemePreference = CursorUtil.getColumnIndexOrThrow(_cursor, "themePreference");
          final int _cursorIndexOfFontScale = CursorUtil.getColumnIndexOrThrow(_cursor, "fontScale");
          final int _cursorIndexOfHighContrastMode = CursorUtil.getColumnIndexOrThrow(_cursor, "highContrastMode");
          final int _cursorIndexOfVoiceInputEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "voiceInputEnabled");
          final int _cursorIndexOfAnimationsEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "animationsEnabled");
          final int _cursorIndexOfWeeklyIncome = CursorUtil.getColumnIndexOrThrow(_cursor, "weeklyIncome");
          final int _cursorIndexOfMonthlyIncome = CursorUtil.getColumnIndexOrThrow(_cursor, "monthlyIncome");
          final int _cursorIndexOfUseZeroBasedBudgeting = CursorUtil.getColumnIndexOrThrow(_cursor, "useZeroBasedBudgeting");
          final int _cursorIndexOfEnvelopeViewEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "envelopeViewEnabled");
          final int _cursorIndexOfImpulseControlEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "impulseControlEnabled");
          final int _cursorIndexOfSpendingThreshold = CursorUtil.getColumnIndexOrThrow(_cursor, "spendingThreshold");
          final int _cursorIndexOfCoolingOffPeriodSeconds = CursorUtil.getColumnIndexOrThrow(_cursor, "coolingOffPeriodSeconds");
          final int _cursorIndexOfEnableReflectionQuestions = CursorUtil.getColumnIndexOrThrow(_cursor, "enableReflectionQuestions");
          final int _cursorIndexOfEnableBudgetWarnings = CursorUtil.getColumnIndexOrThrow(_cursor, "enableBudgetWarnings");
          final int _cursorIndexOfEnableWishlistSuggestions = CursorUtil.getColumnIndexOrThrow(_cursor, "enableWishlistSuggestions");
          final UserPreferences _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpBudgetPeriod;
            if (_cursor.isNull(_cursorIndexOfBudgetPeriod)) {
              _tmpBudgetPeriod = null;
            } else {
              _tmpBudgetPeriod = _cursor.getString(_cursorIndexOfBudgetPeriod);
            }
            final boolean _tmpNotificationsEnabled;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfNotificationsEnabled);
            _tmpNotificationsEnabled = _tmp != 0;
            final String _tmpReminderTime;
            if (_cursor.isNull(_cursorIndexOfReminderTime)) {
              _tmpReminderTime = null;
            } else {
              _tmpReminderTime = _cursor.getString(_cursorIndexOfReminderTime);
            }
            final boolean _tmpDarkModeEnabled;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfDarkModeEnabled);
            _tmpDarkModeEnabled = _tmp_1 != 0;
            final String _tmpFontSize;
            if (_cursor.isNull(_cursorIndexOfFontSize)) {
              _tmpFontSize = null;
            } else {
              _tmpFontSize = _cursor.getString(_cursorIndexOfFontSize);
            }
            final String _tmpPrimaryGoal;
            if (_cursor.isNull(_cursorIndexOfPrimaryGoal)) {
              _tmpPrimaryGoal = null;
            } else {
              _tmpPrimaryGoal = _cursor.getString(_cursorIndexOfPrimaryGoal);
            }
            final Double _tmpWeeklyBudget;
            if (_cursor.isNull(_cursorIndexOfWeeklyBudget)) {
              _tmpWeeklyBudget = null;
            } else {
              _tmpWeeklyBudget = _cursor.getDouble(_cursorIndexOfWeeklyBudget);
            }
            final Double _tmpMonthlyBudget;
            if (_cursor.isNull(_cursorIndexOfMonthlyBudget)) {
              _tmpMonthlyBudget = null;
            } else {
              _tmpMonthlyBudget = _cursor.getDouble(_cursorIndexOfMonthlyBudget);
            }
            final boolean _tmpHasCompletedOnboarding;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfHasCompletedOnboarding);
            _tmpHasCompletedOnboarding = _tmp_2 != 0;
            final boolean _tmpEnableNotifications;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfEnableNotifications);
            _tmpEnableNotifications = _tmp_3 != 0;
            final String _tmpNotificationTime;
            if (_cursor.isNull(_cursorIndexOfNotificationTime)) {
              _tmpNotificationTime = null;
            } else {
              _tmpNotificationTime = _cursor.getString(_cursorIndexOfNotificationTime);
            }
            final String _tmpTheme;
            if (_cursor.isNull(_cursorIndexOfTheme)) {
              _tmpTheme = null;
            } else {
              _tmpTheme = _cursor.getString(_cursorIndexOfTheme);
            }
            final String _tmpThemePreference;
            if (_cursor.isNull(_cursorIndexOfThemePreference)) {
              _tmpThemePreference = null;
            } else {
              _tmpThemePreference = _cursor.getString(_cursorIndexOfThemePreference);
            }
            final float _tmpFontScale;
            _tmpFontScale = _cursor.getFloat(_cursorIndexOfFontScale);
            final boolean _tmpHighContrastMode;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfHighContrastMode);
            _tmpHighContrastMode = _tmp_4 != 0;
            final boolean _tmpVoiceInputEnabled;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfVoiceInputEnabled);
            _tmpVoiceInputEnabled = _tmp_5 != 0;
            final boolean _tmpAnimationsEnabled;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfAnimationsEnabled);
            _tmpAnimationsEnabled = _tmp_6 != 0;
            final Double _tmpWeeklyIncome;
            if (_cursor.isNull(_cursorIndexOfWeeklyIncome)) {
              _tmpWeeklyIncome = null;
            } else {
              _tmpWeeklyIncome = _cursor.getDouble(_cursorIndexOfWeeklyIncome);
            }
            final Double _tmpMonthlyIncome;
            if (_cursor.isNull(_cursorIndexOfMonthlyIncome)) {
              _tmpMonthlyIncome = null;
            } else {
              _tmpMonthlyIncome = _cursor.getDouble(_cursorIndexOfMonthlyIncome);
            }
            final boolean _tmpUseZeroBasedBudgeting;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfUseZeroBasedBudgeting);
            _tmpUseZeroBasedBudgeting = _tmp_7 != 0;
            final boolean _tmpEnvelopeViewEnabled;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfEnvelopeViewEnabled);
            _tmpEnvelopeViewEnabled = _tmp_8 != 0;
            final boolean _tmpImpulseControlEnabled;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfImpulseControlEnabled);
            _tmpImpulseControlEnabled = _tmp_9 != 0;
            final double _tmpSpendingThreshold;
            _tmpSpendingThreshold = _cursor.getDouble(_cursorIndexOfSpendingThreshold);
            final int _tmpCoolingOffPeriodSeconds;
            _tmpCoolingOffPeriodSeconds = _cursor.getInt(_cursorIndexOfCoolingOffPeriodSeconds);
            final boolean _tmpEnableReflectionQuestions;
            final int _tmp_10;
            _tmp_10 = _cursor.getInt(_cursorIndexOfEnableReflectionQuestions);
            _tmpEnableReflectionQuestions = _tmp_10 != 0;
            final boolean _tmpEnableBudgetWarnings;
            final int _tmp_11;
            _tmp_11 = _cursor.getInt(_cursorIndexOfEnableBudgetWarnings);
            _tmpEnableBudgetWarnings = _tmp_11 != 0;
            final boolean _tmpEnableWishlistSuggestions;
            final int _tmp_12;
            _tmp_12 = _cursor.getInt(_cursorIndexOfEnableWishlistSuggestions);
            _tmpEnableWishlistSuggestions = _tmp_12 != 0;
            _result = new UserPreferences(_tmpId,_tmpBudgetPeriod,_tmpNotificationsEnabled,_tmpReminderTime,_tmpDarkModeEnabled,_tmpFontSize,_tmpPrimaryGoal,_tmpWeeklyBudget,_tmpMonthlyBudget,_tmpHasCompletedOnboarding,_tmpEnableNotifications,_tmpNotificationTime,_tmpTheme,_tmpThemePreference,_tmpFontScale,_tmpHighContrastMode,_tmpVoiceInputEnabled,_tmpAnimationsEnabled,_tmpWeeklyIncome,_tmpMonthlyIncome,_tmpUseZeroBasedBudgeting,_tmpEnvelopeViewEnabled,_tmpImpulseControlEnabled,_tmpSpendingThreshold,_tmpCoolingOffPeriodSeconds,_tmpEnableReflectionQuestions,_tmpEnableBudgetWarnings,_tmpEnableWishlistSuggestions);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
