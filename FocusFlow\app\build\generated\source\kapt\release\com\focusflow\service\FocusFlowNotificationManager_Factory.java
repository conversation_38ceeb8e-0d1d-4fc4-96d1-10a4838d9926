package com.focusflow.service;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FocusFlowNotificationManager_Factory implements Factory<FocusFlowNotificationManager> {
  private final Provider<Context> contextProvider;

  public FocusFlowNotificationManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public FocusFlowNotificationManager get() {
    return newInstance(contextProvider.get());
  }

  public static FocusFlowNotificationManager_Factory create(Provider<Context> contextProvider) {
    return new FocusFlowNotificationManager_Factory(contextProvider);
  }

  public static FocusFlowNotificationManager newInstance(Context context) {
    return new FocusFlowNotificationManager(context);
  }
}
