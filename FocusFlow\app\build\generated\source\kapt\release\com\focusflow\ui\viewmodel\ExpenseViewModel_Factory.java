package com.focusflow.ui.viewmodel;

import com.focusflow.data.repository.ExpenseRepository;
import com.focusflow.data.repository.UserPreferencesRepository;
import com.focusflow.service.GamificationService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ExpenseViewModel_Factory implements Factory<ExpenseViewModel> {
  private final Provider<ExpenseRepository> expenseRepositoryProvider;

  private final Provider<UserPreferencesRepository> userPreferencesRepositoryProvider;

  private final Provider<GamificationService> gamificationServiceProvider;

  public ExpenseViewModel_Factory(Provider<ExpenseRepository> expenseRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<GamificationService> gamificationServiceProvider) {
    this.expenseRepositoryProvider = expenseRepositoryProvider;
    this.userPreferencesRepositoryProvider = userPreferencesRepositoryProvider;
    this.gamificationServiceProvider = gamificationServiceProvider;
  }

  @Override
  public ExpenseViewModel get() {
    return newInstance(expenseRepositoryProvider.get(), userPreferencesRepositoryProvider.get(), gamificationServiceProvider.get());
  }

  public static ExpenseViewModel_Factory create(
      Provider<ExpenseRepository> expenseRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<GamificationService> gamificationServiceProvider) {
    return new ExpenseViewModel_Factory(expenseRepositoryProvider, userPreferencesRepositoryProvider, gamificationServiceProvider);
  }

  public static ExpenseViewModel newInstance(ExpenseRepository expenseRepository,
      UserPreferencesRepository userPreferencesRepository,
      GamificationService gamificationService) {
    return new ExpenseViewModel(expenseRepository, userPreferencesRepository, gamificationService);
  }
}
