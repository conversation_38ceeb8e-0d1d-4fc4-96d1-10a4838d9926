package com.focusflow.ui.viewmodel;

import com.focusflow.data.repository.BudgetCategoryRepository;
import com.focusflow.data.repository.CreditCardRepository;
import com.focusflow.data.repository.PayoffPlanRepository;
import com.focusflow.data.repository.UserPreferencesRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PayoffPlannerViewModel_Factory implements Factory<PayoffPlannerViewModel> {
  private final Provider<CreditCardRepository> creditCardRepositoryProvider;

  private final Provider<PayoffPlanRepository> payoffPlanRepositoryProvider;

  private final Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider;

  private final Provider<UserPreferencesRepository> userPreferencesRepositoryProvider;

  public PayoffPlannerViewModel_Factory(Provider<CreditCardRepository> creditCardRepositoryProvider,
      Provider<PayoffPlanRepository> payoffPlanRepositoryProvider,
      Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider) {
    this.creditCardRepositoryProvider = creditCardRepositoryProvider;
    this.payoffPlanRepositoryProvider = payoffPlanRepositoryProvider;
    this.budgetCategoryRepositoryProvider = budgetCategoryRepositoryProvider;
    this.userPreferencesRepositoryProvider = userPreferencesRepositoryProvider;
  }

  @Override
  public PayoffPlannerViewModel get() {
    return newInstance(creditCardRepositoryProvider.get(), payoffPlanRepositoryProvider.get(), budgetCategoryRepositoryProvider.get(), userPreferencesRepositoryProvider.get());
  }

  public static PayoffPlannerViewModel_Factory create(
      Provider<CreditCardRepository> creditCardRepositoryProvider,
      Provider<PayoffPlanRepository> payoffPlanRepositoryProvider,
      Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider) {
    return new PayoffPlannerViewModel_Factory(creditCardRepositoryProvider, payoffPlanRepositoryProvider, budgetCategoryRepositoryProvider, userPreferencesRepositoryProvider);
  }

  public static PayoffPlannerViewModel newInstance(CreditCardRepository creditCardRepository,
      PayoffPlanRepository payoffPlanRepository, BudgetCategoryRepository budgetCategoryRepository,
      UserPreferencesRepository userPreferencesRepository) {
    return new PayoffPlannerViewModel(creditCardRepository, payoffPlanRepository, budgetCategoryRepository, userPreferencesRepository);
  }
}
