package com.focusflow.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001e\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001BY\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\t\u0012\u0006\u0010\u000b\u001a\u00020\t\u0012\u0006\u0010\f\u001a\u00020\t\u0012\b\b\u0002\u0010\r\u001a\u00020\u000e\u0012\u0006\u0010\u000f\u001a\u00020\u0010\u00a2\u0006\u0002\u0010\u0011J\t\u0010 \u001a\u00020\u0003H\u00c6\u0003J\t\u0010!\u001a\u00020\u0010H\u00c6\u0003J\t\u0010\"\u001a\u00020\u0003H\u00c6\u0003J\t\u0010#\u001a\u00020\u0003H\u00c6\u0003J\t\u0010$\u001a\u00020\u0007H\u00c6\u0003J\t\u0010%\u001a\u00020\tH\u00c6\u0003J\t\u0010&\u001a\u00020\tH\u00c6\u0003J\t\u0010\'\u001a\u00020\tH\u00c6\u0003J\t\u0010(\u001a\u00020\tH\u00c6\u0003J\t\u0010)\u001a\u00020\u000eH\u00c6\u0003Jm\u0010*\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\t2\b\b\u0002\u0010\f\u001a\u00020\t2\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u0010H\u00c6\u0001J\u0013\u0010+\u001a\u00020\u000e2\b\u0010,\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010-\u001a\u00020\u0007H\u00d6\u0001J\t\u0010.\u001a\u00020/H\u00d6\u0001R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u000f\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0013R\u0011\u0010\u000b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u0019R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0013R\u0011\u0010\n\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0018R\u0011\u0010\f\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0018R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0018\u00a8\u00060"}, d2 = {"Lcom/focusflow/data/model/PaymentSchedule;", "", "id", "", "payoffPlanId", "creditCardId", "month", "", "scheduledPayment", "", "principalAmount", "interestAmount", "remainingBalance", "isExtraPayment", "", "dueDate", "Lkotlinx/datetime/LocalDate;", "(JJJIDDDDZLkotlinx/datetime/LocalDate;)V", "getCreditCardId", "()J", "getDueDate", "()Lkotlinx/datetime/LocalDate;", "getId", "getInterestAmount", "()D", "()Z", "getMonth", "()I", "getPayoffPlanId", "getPrincipalAmount", "getRemainingBalance", "getScheduledPayment", "component1", "component10", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "", "app_release"})
@androidx.room.Entity(tableName = "payment_schedules")
public final class PaymentSchedule {
    @androidx.room.PrimaryKey(autoGenerate = true)
    private final long id = 0L;
    private final long payoffPlanId = 0L;
    private final long creditCardId = 0L;
    private final int month = 0;
    private final double scheduledPayment = 0.0;
    private final double principalAmount = 0.0;
    private final double interestAmount = 0.0;
    private final double remainingBalance = 0.0;
    private final boolean isExtraPayment = false;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.datetime.LocalDate dueDate = null;
    
    public PaymentSchedule(long id, long payoffPlanId, long creditCardId, int month, double scheduledPayment, double principalAmount, double interestAmount, double remainingBalance, boolean isExtraPayment, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate dueDate) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    public final long getPayoffPlanId() {
        return 0L;
    }
    
    public final long getCreditCardId() {
        return 0L;
    }
    
    public final int getMonth() {
        return 0;
    }
    
    public final double getScheduledPayment() {
        return 0.0;
    }
    
    public final double getPrincipalAmount() {
        return 0.0;
    }
    
    public final double getInterestAmount() {
        return 0.0;
    }
    
    public final double getRemainingBalance() {
        return 0.0;
    }
    
    public final boolean isExtraPayment() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDate getDueDate() {
        return null;
    }
    
    public final long component1() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDate component10() {
        return null;
    }
    
    public final long component2() {
        return 0L;
    }
    
    public final long component3() {
        return 0L;
    }
    
    public final int component4() {
        return 0;
    }
    
    public final double component5() {
        return 0.0;
    }
    
    public final double component6() {
        return 0.0;
    }
    
    public final double component7() {
        return 0.0;
    }
    
    public final double component8() {
        return 0.0;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.PaymentSchedule copy(long id, long payoffPlanId, long creditCardId, int month, double scheduledPayment, double principalAmount, double interestAmount, double remainingBalance, boolean isExtraPayment, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate dueDate) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}