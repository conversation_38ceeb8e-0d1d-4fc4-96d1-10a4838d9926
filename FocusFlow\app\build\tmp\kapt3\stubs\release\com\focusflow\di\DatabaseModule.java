package com.focusflow.di;

@dagger.Module
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00a4\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\r\u001a\u00020\u000e2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0012\u0010\u0017\u001a\u00020\u00062\b\b\u0001\u0010\u0018\u001a\u00020\u0019H\u0007J\u0010\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u001e\u001a\u00020\u001f2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010 \u001a\u00020!2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\"\u001a\u00020#2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010$\u001a\u00020%2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010&\u001a\u00020\'2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010(\u001a\u00020)2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010*\u001a\u00020+2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010,\u001a\u00020-2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010.\u001a\u00020/2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u00100\u001a\u0002012\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u00102\u001a\u0002032\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u00104\u001a\u0002052\u0006\u0010\u0005\u001a\u00020\u0006H\u0007\u00a8\u00066"}, d2 = {"Lcom/focusflow/di/DatabaseModule;", "", "()V", "provideAIInteractionDao", "Lcom/focusflow/data/dao/AIInteractionDao;", "database", "Lcom/focusflow/data/database/FocusFlowDatabase;", "provideAccountabilityContactDao", "Lcom/focusflow/data/dao/AccountabilityContactDao;", "provideAchievementDao", "Lcom/focusflow/data/dao/AchievementDao;", "provideAlternativeProductDao", "Lcom/focusflow/data/dao/AlternativeProductDao;", "provideBudgetAnalyticsDao", "Lcom/focusflow/data/dao/BudgetAnalyticsDao;", "provideBudgetCategoryDao", "Lcom/focusflow/data/dao/BudgetCategoryDao;", "provideBudgetRecommendationDao", "Lcom/focusflow/data/dao/BudgetRecommendationDao;", "provideCreditCardDao", "Lcom/focusflow/data/dao/CreditCardDao;", "provideDashboardWidgetDao", "Lcom/focusflow/data/dao/DashboardWidgetDao;", "provideDatabase", "context", "Landroid/content/Context;", "provideExpenseDao", "Lcom/focusflow/data/dao/ExpenseDao;", "provideFocusSessionDao", "Lcom/focusflow/data/dao/FocusSessionDao;", "provideHabitLogDao", "Lcom/focusflow/data/dao/HabitLogDao;", "providePaymentScheduleDao", "Lcom/focusflow/data/dao/PaymentScheduleDao;", "providePayoffMilestoneDao", "Lcom/focusflow/data/dao/PayoffMilestoneDao;", "providePayoffPlanDao", "Lcom/focusflow/data/dao/PayoffPlanDao;", "provideSpendingPatternDao", "Lcom/focusflow/data/dao/SpendingPatternDao;", "provideSpendingReflectionDao", "Lcom/focusflow/data/dao/SpendingReflectionDao;", "provideTaskDao", "Lcom/focusflow/data/dao/TaskDao;", "provideUserPreferencesDao", "Lcom/focusflow/data/dao/UserPreferencesDao;", "provideUserStatsDao", "Lcom/focusflow/data/dao/UserStatsDao;", "provideVirtualPetDao", "Lcom/focusflow/data/dao/VirtualPetDao;", "provideVoiceCommandDao", "Lcom/focusflow/data/dao/VoiceCommandDao;", "provideWishlistItemDao", "Lcom/focusflow/data/dao/WishlistItemDao;", "app_release"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class DatabaseModule {
    @org.jetbrains.annotations.NotNull
    public static final com.focusflow.di.DatabaseModule INSTANCE = null;
    
    private DatabaseModule() {
        super();
    }
    
    @dagger.Provides
    @javax.inject.Singleton
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.database.FocusFlowDatabase provideDatabase(@dagger.hilt.android.qualifiers.ApplicationContext
    @org.jetbrains.annotations.NotNull
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.ExpenseDao provideExpenseDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.CreditCardDao provideCreditCardDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.BudgetCategoryDao provideBudgetCategoryDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.HabitLogDao provideHabitLogDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.TaskDao provideTaskDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.AIInteractionDao provideAIInteractionDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.UserPreferencesDao provideUserPreferencesDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.AchievementDao provideAchievementDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.UserStatsDao provideUserStatsDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.VirtualPetDao provideVirtualPetDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.WishlistItemDao provideWishlistItemDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.BudgetRecommendationDao provideBudgetRecommendationDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.SpendingReflectionDao provideSpendingReflectionDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.BudgetAnalyticsDao provideBudgetAnalyticsDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.FocusSessionDao provideFocusSessionDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.SpendingPatternDao provideSpendingPatternDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.AlternativeProductDao provideAlternativeProductDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.AccountabilityContactDao provideAccountabilityContactDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.DashboardWidgetDao provideDashboardWidgetDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.VoiceCommandDao provideVoiceCommandDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.PayoffPlanDao providePayoffPlanDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.PaymentScheduleDao providePaymentScheduleDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.PayoffMilestoneDao providePayoffMilestoneDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
}