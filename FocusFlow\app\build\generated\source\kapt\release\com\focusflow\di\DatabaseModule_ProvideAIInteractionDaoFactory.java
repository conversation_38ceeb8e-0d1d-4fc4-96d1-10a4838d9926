package com.focusflow.di;

import com.focusflow.data.dao.AIInteractionDao;
import com.focusflow.data.database.FocusFlowDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideAIInteractionDaoFactory implements Factory<AIInteractionDao> {
  private final Provider<FocusFlowDatabase> databaseProvider;

  public DatabaseModule_ProvideAIInteractionDaoFactory(
      Provider<FocusFlowDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public AIInteractionDao get() {
    return provideAIInteractionDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideAIInteractionDaoFactory create(
      Provider<FocusFlowDatabase> databaseProvider) {
    return new DatabaseModule_ProvideAIInteractionDaoFactory(databaseProvider);
  }

  public static AIInteractionDao provideAIInteractionDao(FocusFlowDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideAIInteractionDao(database));
  }
}
