package com.focusflow.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\"\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0006\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\u001aH\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032&\u0010\u0004\u001a\"\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0007\u0012\u0006\u0012\u0004\u0018\u00010\u0007\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u00a8\u0006\n"}, d2 = {"EnhancedAddExpenseDialog", "", "onDismiss", "Lkotlin/Function0;", "onAddExpense", "Lkotlin/Function4;", "", "", "impulseControlViewModel", "Lcom/focusflow/ui/viewmodel/ImpulseControlViewModel;", "app_release"})
public final class EnhancedExpenseDialogKt {
    
    @androidx.compose.runtime.Composable
    public static final void EnhancedAddExpenseDialog(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function4<? super java.lang.Double, ? super java.lang.String, ? super java.lang.String, ? super java.lang.String, kotlin.Unit> onAddExpense, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.ImpulseControlViewModel impulseControlViewModel) {
    }
}