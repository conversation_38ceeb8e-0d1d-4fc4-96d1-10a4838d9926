package com.focusflow.data.repository;

import com.focusflow.data.dao.PaymentScheduleDao;
import com.focusflow.data.dao.PayoffMilestoneDao;
import com.focusflow.data.dao.PayoffPlanDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PayoffPlanRepository_Factory implements Factory<PayoffPlanRepository> {
  private final Provider<PayoffPlanDao> payoffPlanDaoProvider;

  private final Provider<PaymentScheduleDao> paymentScheduleDaoProvider;

  private final Provider<PayoffMilestoneDao> payoffMilestoneDaoProvider;

  public PayoffPlanRepository_Factory(Provider<PayoffPlanDao> payoffPlanDaoProvider,
      Provider<PaymentScheduleDao> paymentScheduleDaoProvider,
      Provider<PayoffMilestoneDao> payoffMilestoneDaoProvider) {
    this.payoffPlanDaoProvider = payoffPlanDaoProvider;
    this.paymentScheduleDaoProvider = paymentScheduleDaoProvider;
    this.payoffMilestoneDaoProvider = payoffMilestoneDaoProvider;
  }

  @Override
  public PayoffPlanRepository get() {
    return newInstance(payoffPlanDaoProvider.get(), paymentScheduleDaoProvider.get(), payoffMilestoneDaoProvider.get());
  }

  public static PayoffPlanRepository_Factory create(Provider<PayoffPlanDao> payoffPlanDaoProvider,
      Provider<PaymentScheduleDao> paymentScheduleDaoProvider,
      Provider<PayoffMilestoneDao> payoffMilestoneDaoProvider) {
    return new PayoffPlanRepository_Factory(payoffPlanDaoProvider, paymentScheduleDaoProvider, payoffMilestoneDaoProvider);
  }

  public static PayoffPlanRepository newInstance(PayoffPlanDao payoffPlanDao,
      PaymentScheduleDao paymentScheduleDao, PayoffMilestoneDao payoffMilestoneDao) {
    return new PayoffPlanRepository(payoffPlanDao, paymentScheduleDao, payoffMilestoneDao);
  }
}
