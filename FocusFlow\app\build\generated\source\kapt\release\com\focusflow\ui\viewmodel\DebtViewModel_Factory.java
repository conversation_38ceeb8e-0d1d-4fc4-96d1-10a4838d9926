package com.focusflow.ui.viewmodel;

import com.focusflow.data.repository.CreditCardRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DebtViewModel_Factory implements Factory<DebtViewModel> {
  private final Provider<CreditCardRepository> creditCardRepositoryProvider;

  public DebtViewModel_Factory(Provider<CreditCardRepository> creditCardRepositoryProvider) {
    this.creditCardRepositoryProvider = creditCardRepositoryProvider;
  }

  @Override
  public DebtViewModel get() {
    return newInstance(creditCardRepositoryProvider.get());
  }

  public static DebtViewModel_Factory create(
      Provider<CreditCardRepository> creditCardRepositoryProvider) {
    return new DebtViewModel_Factory(creditCardRepositoryProvider);
  }

  public static DebtViewModel newInstance(CreditCardRepository creditCardRepository) {
    return new DebtViewModel(creditCardRepository);
  }
}
