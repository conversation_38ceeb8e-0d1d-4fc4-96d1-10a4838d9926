package com.focusflow.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.focusflow.data.database.Converters;
import com.focusflow.data.model.VoiceCommand;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;
import kotlinx.datetime.LocalDateTime;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class VoiceCommandDao_Impl implements VoiceCommandDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<VoiceCommand> __insertionAdapterOfVoiceCommand;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<VoiceCommand> __deletionAdapterOfVoiceCommand;

  private final EntityDeletionOrUpdateAdapter<VoiceCommand> __updateAdapterOfVoiceCommand;

  private final SharedSQLiteStatement __preparedStmtOfUpdateUserFeedback;

  private final SharedSQLiteStatement __preparedStmtOfUpdateCorrectedCommand;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldCommands;

  public VoiceCommandDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfVoiceCommand = new EntityInsertionAdapter<VoiceCommand>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `voice_commands` (`id`,`commandText`,`recognizedText`,`commandType`,`intent`,`parameters`,`isSuccessful`,`confidence`,`processingTime`,`timestamp`,`userId`,`sessionId`,`context`,`followUpRequired`,`followUpPrompt`,`errorMessage`,`correctedCommand`,`actionTaken`,`resultData`,`userFeedback`,`language`,`accent`,`noiseLevel`,`deviceType`,`isOffline`,`retryCount`,`alternativeCommands`,`learningData`,`privacyLevel`,`isTrainingData`,`customVocabulary`,`shortcuts`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final VoiceCommand entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getCommandText() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getCommandText());
        }
        if (entity.getRecognizedText() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getRecognizedText());
        }
        if (entity.getCommandType() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getCommandType());
        }
        if (entity.getIntent() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getIntent());
        }
        if (entity.getParameters() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getParameters());
        }
        final int _tmp = entity.isSuccessful() ? 1 : 0;
        statement.bindLong(7, _tmp);
        statement.bindDouble(8, entity.getConfidence());
        statement.bindLong(9, entity.getProcessingTime());
        final String _tmp_1 = __converters.fromLocalDateTime(entity.getTimestamp());
        if (_tmp_1 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_1);
        }
        if (entity.getUserId() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getUserId());
        }
        if (entity.getSessionId() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getSessionId());
        }
        if (entity.getContext() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getContext());
        }
        final int _tmp_2 = entity.getFollowUpRequired() ? 1 : 0;
        statement.bindLong(14, _tmp_2);
        if (entity.getFollowUpPrompt() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getFollowUpPrompt());
        }
        if (entity.getErrorMessage() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getErrorMessage());
        }
        if (entity.getCorrectedCommand() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getCorrectedCommand());
        }
        if (entity.getActionTaken() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getActionTaken());
        }
        if (entity.getResultData() == null) {
          statement.bindNull(19);
        } else {
          statement.bindString(19, entity.getResultData());
        }
        if (entity.getUserFeedback() == null) {
          statement.bindNull(20);
        } else {
          statement.bindString(20, entity.getUserFeedback());
        }
        if (entity.getLanguage() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getLanguage());
        }
        if (entity.getAccent() == null) {
          statement.bindNull(22);
        } else {
          statement.bindString(22, entity.getAccent());
        }
        if (entity.getNoiseLevel() == null) {
          statement.bindNull(23);
        } else {
          statement.bindString(23, entity.getNoiseLevel());
        }
        if (entity.getDeviceType() == null) {
          statement.bindNull(24);
        } else {
          statement.bindString(24, entity.getDeviceType());
        }
        final int _tmp_3 = entity.isOffline() ? 1 : 0;
        statement.bindLong(25, _tmp_3);
        statement.bindLong(26, entity.getRetryCount());
        if (entity.getAlternativeCommands() == null) {
          statement.bindNull(27);
        } else {
          statement.bindString(27, entity.getAlternativeCommands());
        }
        if (entity.getLearningData() == null) {
          statement.bindNull(28);
        } else {
          statement.bindString(28, entity.getLearningData());
        }
        if (entity.getPrivacyLevel() == null) {
          statement.bindNull(29);
        } else {
          statement.bindString(29, entity.getPrivacyLevel());
        }
        final int _tmp_4 = entity.isTrainingData() ? 1 : 0;
        statement.bindLong(30, _tmp_4);
        if (entity.getCustomVocabulary() == null) {
          statement.bindNull(31);
        } else {
          statement.bindString(31, entity.getCustomVocabulary());
        }
        if (entity.getShortcuts() == null) {
          statement.bindNull(32);
        } else {
          statement.bindString(32, entity.getShortcuts());
        }
      }
    };
    this.__deletionAdapterOfVoiceCommand = new EntityDeletionOrUpdateAdapter<VoiceCommand>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `voice_commands` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final VoiceCommand entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfVoiceCommand = new EntityDeletionOrUpdateAdapter<VoiceCommand>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `voice_commands` SET `id` = ?,`commandText` = ?,`recognizedText` = ?,`commandType` = ?,`intent` = ?,`parameters` = ?,`isSuccessful` = ?,`confidence` = ?,`processingTime` = ?,`timestamp` = ?,`userId` = ?,`sessionId` = ?,`context` = ?,`followUpRequired` = ?,`followUpPrompt` = ?,`errorMessage` = ?,`correctedCommand` = ?,`actionTaken` = ?,`resultData` = ?,`userFeedback` = ?,`language` = ?,`accent` = ?,`noiseLevel` = ?,`deviceType` = ?,`isOffline` = ?,`retryCount` = ?,`alternativeCommands` = ?,`learningData` = ?,`privacyLevel` = ?,`isTrainingData` = ?,`customVocabulary` = ?,`shortcuts` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final VoiceCommand entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getCommandText() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getCommandText());
        }
        if (entity.getRecognizedText() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getRecognizedText());
        }
        if (entity.getCommandType() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getCommandType());
        }
        if (entity.getIntent() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getIntent());
        }
        if (entity.getParameters() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getParameters());
        }
        final int _tmp = entity.isSuccessful() ? 1 : 0;
        statement.bindLong(7, _tmp);
        statement.bindDouble(8, entity.getConfidence());
        statement.bindLong(9, entity.getProcessingTime());
        final String _tmp_1 = __converters.fromLocalDateTime(entity.getTimestamp());
        if (_tmp_1 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_1);
        }
        if (entity.getUserId() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getUserId());
        }
        if (entity.getSessionId() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getSessionId());
        }
        if (entity.getContext() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getContext());
        }
        final int _tmp_2 = entity.getFollowUpRequired() ? 1 : 0;
        statement.bindLong(14, _tmp_2);
        if (entity.getFollowUpPrompt() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getFollowUpPrompt());
        }
        if (entity.getErrorMessage() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getErrorMessage());
        }
        if (entity.getCorrectedCommand() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getCorrectedCommand());
        }
        if (entity.getActionTaken() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getActionTaken());
        }
        if (entity.getResultData() == null) {
          statement.bindNull(19);
        } else {
          statement.bindString(19, entity.getResultData());
        }
        if (entity.getUserFeedback() == null) {
          statement.bindNull(20);
        } else {
          statement.bindString(20, entity.getUserFeedback());
        }
        if (entity.getLanguage() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getLanguage());
        }
        if (entity.getAccent() == null) {
          statement.bindNull(22);
        } else {
          statement.bindString(22, entity.getAccent());
        }
        if (entity.getNoiseLevel() == null) {
          statement.bindNull(23);
        } else {
          statement.bindString(23, entity.getNoiseLevel());
        }
        if (entity.getDeviceType() == null) {
          statement.bindNull(24);
        } else {
          statement.bindString(24, entity.getDeviceType());
        }
        final int _tmp_3 = entity.isOffline() ? 1 : 0;
        statement.bindLong(25, _tmp_3);
        statement.bindLong(26, entity.getRetryCount());
        if (entity.getAlternativeCommands() == null) {
          statement.bindNull(27);
        } else {
          statement.bindString(27, entity.getAlternativeCommands());
        }
        if (entity.getLearningData() == null) {
          statement.bindNull(28);
        } else {
          statement.bindString(28, entity.getLearningData());
        }
        if (entity.getPrivacyLevel() == null) {
          statement.bindNull(29);
        } else {
          statement.bindString(29, entity.getPrivacyLevel());
        }
        final int _tmp_4 = entity.isTrainingData() ? 1 : 0;
        statement.bindLong(30, _tmp_4);
        if (entity.getCustomVocabulary() == null) {
          statement.bindNull(31);
        } else {
          statement.bindString(31, entity.getCustomVocabulary());
        }
        if (entity.getShortcuts() == null) {
          statement.bindNull(32);
        } else {
          statement.bindString(32, entity.getShortcuts());
        }
        statement.bindLong(33, entity.getId());
      }
    };
    this.__preparedStmtOfUpdateUserFeedback = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE voice_commands SET userFeedback = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateCorrectedCommand = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE voice_commands SET correctedCommand = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteOldCommands = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM voice_commands WHERE timestamp < ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertVoiceCommand(final VoiceCommand voiceCommand,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfVoiceCommand.insertAndReturnId(voiceCommand);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteVoiceCommand(final VoiceCommand voiceCommand,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfVoiceCommand.handle(voiceCommand);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateVoiceCommand(final VoiceCommand voiceCommand,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfVoiceCommand.handle(voiceCommand);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateUserFeedback(final long id, final String feedback,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateUserFeedback.acquire();
        int _argIndex = 1;
        if (feedback == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, feedback);
        }
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateUserFeedback.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateCorrectedCommand(final long id, final String correctedCommand,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateCorrectedCommand.acquire();
        int _argIndex = 1;
        if (correctedCommand == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, correctedCommand);
        }
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateCorrectedCommand.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteOldCommands(final LocalDateTime cutoffDate,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldCommands.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromLocalDateTime(cutoffDate);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldCommands.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<VoiceCommand>> getAllVoiceCommands() {
    final String _sql = "SELECT * FROM voice_commands ORDER BY timestamp DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"voice_commands"}, new Callable<List<VoiceCommand>>() {
      @Override
      @NonNull
      public List<VoiceCommand> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCommandText = CursorUtil.getColumnIndexOrThrow(_cursor, "commandText");
          final int _cursorIndexOfRecognizedText = CursorUtil.getColumnIndexOrThrow(_cursor, "recognizedText");
          final int _cursorIndexOfCommandType = CursorUtil.getColumnIndexOrThrow(_cursor, "commandType");
          final int _cursorIndexOfIntent = CursorUtil.getColumnIndexOrThrow(_cursor, "intent");
          final int _cursorIndexOfParameters = CursorUtil.getColumnIndexOrThrow(_cursor, "parameters");
          final int _cursorIndexOfIsSuccessful = CursorUtil.getColumnIndexOrThrow(_cursor, "isSuccessful");
          final int _cursorIndexOfConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "confidence");
          final int _cursorIndexOfProcessingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "processingTime");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfSessionId = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionId");
          final int _cursorIndexOfContext = CursorUtil.getColumnIndexOrThrow(_cursor, "context");
          final int _cursorIndexOfFollowUpRequired = CursorUtil.getColumnIndexOrThrow(_cursor, "followUpRequired");
          final int _cursorIndexOfFollowUpPrompt = CursorUtil.getColumnIndexOrThrow(_cursor, "followUpPrompt");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfCorrectedCommand = CursorUtil.getColumnIndexOrThrow(_cursor, "correctedCommand");
          final int _cursorIndexOfActionTaken = CursorUtil.getColumnIndexOrThrow(_cursor, "actionTaken");
          final int _cursorIndexOfResultData = CursorUtil.getColumnIndexOrThrow(_cursor, "resultData");
          final int _cursorIndexOfUserFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "userFeedback");
          final int _cursorIndexOfLanguage = CursorUtil.getColumnIndexOrThrow(_cursor, "language");
          final int _cursorIndexOfAccent = CursorUtil.getColumnIndexOrThrow(_cursor, "accent");
          final int _cursorIndexOfNoiseLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "noiseLevel");
          final int _cursorIndexOfDeviceType = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceType");
          final int _cursorIndexOfIsOffline = CursorUtil.getColumnIndexOrThrow(_cursor, "isOffline");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfAlternativeCommands = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeCommands");
          final int _cursorIndexOfLearningData = CursorUtil.getColumnIndexOrThrow(_cursor, "learningData");
          final int _cursorIndexOfPrivacyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "privacyLevel");
          final int _cursorIndexOfIsTrainingData = CursorUtil.getColumnIndexOrThrow(_cursor, "isTrainingData");
          final int _cursorIndexOfCustomVocabulary = CursorUtil.getColumnIndexOrThrow(_cursor, "customVocabulary");
          final int _cursorIndexOfShortcuts = CursorUtil.getColumnIndexOrThrow(_cursor, "shortcuts");
          final List<VoiceCommand> _result = new ArrayList<VoiceCommand>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final VoiceCommand _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCommandText;
            if (_cursor.isNull(_cursorIndexOfCommandText)) {
              _tmpCommandText = null;
            } else {
              _tmpCommandText = _cursor.getString(_cursorIndexOfCommandText);
            }
            final String _tmpRecognizedText;
            if (_cursor.isNull(_cursorIndexOfRecognizedText)) {
              _tmpRecognizedText = null;
            } else {
              _tmpRecognizedText = _cursor.getString(_cursorIndexOfRecognizedText);
            }
            final String _tmpCommandType;
            if (_cursor.isNull(_cursorIndexOfCommandType)) {
              _tmpCommandType = null;
            } else {
              _tmpCommandType = _cursor.getString(_cursorIndexOfCommandType);
            }
            final String _tmpIntent;
            if (_cursor.isNull(_cursorIndexOfIntent)) {
              _tmpIntent = null;
            } else {
              _tmpIntent = _cursor.getString(_cursorIndexOfIntent);
            }
            final String _tmpParameters;
            if (_cursor.isNull(_cursorIndexOfParameters)) {
              _tmpParameters = null;
            } else {
              _tmpParameters = _cursor.getString(_cursorIndexOfParameters);
            }
            final boolean _tmpIsSuccessful;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSuccessful);
            _tmpIsSuccessful = _tmp != 0;
            final double _tmpConfidence;
            _tmpConfidence = _cursor.getDouble(_cursorIndexOfConfidence);
            final long _tmpProcessingTime;
            _tmpProcessingTime = _cursor.getLong(_cursorIndexOfProcessingTime);
            final LocalDateTime _tmpTimestamp;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfTimestamp)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfTimestamp);
            }
            _tmpTimestamp = __converters.toLocalDateTime(_tmp_1);
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final String _tmpSessionId;
            if (_cursor.isNull(_cursorIndexOfSessionId)) {
              _tmpSessionId = null;
            } else {
              _tmpSessionId = _cursor.getString(_cursorIndexOfSessionId);
            }
            final String _tmpContext;
            if (_cursor.isNull(_cursorIndexOfContext)) {
              _tmpContext = null;
            } else {
              _tmpContext = _cursor.getString(_cursorIndexOfContext);
            }
            final boolean _tmpFollowUpRequired;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfFollowUpRequired);
            _tmpFollowUpRequired = _tmp_2 != 0;
            final String _tmpFollowUpPrompt;
            if (_cursor.isNull(_cursorIndexOfFollowUpPrompt)) {
              _tmpFollowUpPrompt = null;
            } else {
              _tmpFollowUpPrompt = _cursor.getString(_cursorIndexOfFollowUpPrompt);
            }
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final String _tmpCorrectedCommand;
            if (_cursor.isNull(_cursorIndexOfCorrectedCommand)) {
              _tmpCorrectedCommand = null;
            } else {
              _tmpCorrectedCommand = _cursor.getString(_cursorIndexOfCorrectedCommand);
            }
            final String _tmpActionTaken;
            if (_cursor.isNull(_cursorIndexOfActionTaken)) {
              _tmpActionTaken = null;
            } else {
              _tmpActionTaken = _cursor.getString(_cursorIndexOfActionTaken);
            }
            final String _tmpResultData;
            if (_cursor.isNull(_cursorIndexOfResultData)) {
              _tmpResultData = null;
            } else {
              _tmpResultData = _cursor.getString(_cursorIndexOfResultData);
            }
            final String _tmpUserFeedback;
            if (_cursor.isNull(_cursorIndexOfUserFeedback)) {
              _tmpUserFeedback = null;
            } else {
              _tmpUserFeedback = _cursor.getString(_cursorIndexOfUserFeedback);
            }
            final String _tmpLanguage;
            if (_cursor.isNull(_cursorIndexOfLanguage)) {
              _tmpLanguage = null;
            } else {
              _tmpLanguage = _cursor.getString(_cursorIndexOfLanguage);
            }
            final String _tmpAccent;
            if (_cursor.isNull(_cursorIndexOfAccent)) {
              _tmpAccent = null;
            } else {
              _tmpAccent = _cursor.getString(_cursorIndexOfAccent);
            }
            final String _tmpNoiseLevel;
            if (_cursor.isNull(_cursorIndexOfNoiseLevel)) {
              _tmpNoiseLevel = null;
            } else {
              _tmpNoiseLevel = _cursor.getString(_cursorIndexOfNoiseLevel);
            }
            final String _tmpDeviceType;
            if (_cursor.isNull(_cursorIndexOfDeviceType)) {
              _tmpDeviceType = null;
            } else {
              _tmpDeviceType = _cursor.getString(_cursorIndexOfDeviceType);
            }
            final boolean _tmpIsOffline;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsOffline);
            _tmpIsOffline = _tmp_3 != 0;
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final String _tmpAlternativeCommands;
            if (_cursor.isNull(_cursorIndexOfAlternativeCommands)) {
              _tmpAlternativeCommands = null;
            } else {
              _tmpAlternativeCommands = _cursor.getString(_cursorIndexOfAlternativeCommands);
            }
            final String _tmpLearningData;
            if (_cursor.isNull(_cursorIndexOfLearningData)) {
              _tmpLearningData = null;
            } else {
              _tmpLearningData = _cursor.getString(_cursorIndexOfLearningData);
            }
            final String _tmpPrivacyLevel;
            if (_cursor.isNull(_cursorIndexOfPrivacyLevel)) {
              _tmpPrivacyLevel = null;
            } else {
              _tmpPrivacyLevel = _cursor.getString(_cursorIndexOfPrivacyLevel);
            }
            final boolean _tmpIsTrainingData;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIsTrainingData);
            _tmpIsTrainingData = _tmp_4 != 0;
            final String _tmpCustomVocabulary;
            if (_cursor.isNull(_cursorIndexOfCustomVocabulary)) {
              _tmpCustomVocabulary = null;
            } else {
              _tmpCustomVocabulary = _cursor.getString(_cursorIndexOfCustomVocabulary);
            }
            final String _tmpShortcuts;
            if (_cursor.isNull(_cursorIndexOfShortcuts)) {
              _tmpShortcuts = null;
            } else {
              _tmpShortcuts = _cursor.getString(_cursorIndexOfShortcuts);
            }
            _item = new VoiceCommand(_tmpId,_tmpCommandText,_tmpRecognizedText,_tmpCommandType,_tmpIntent,_tmpParameters,_tmpIsSuccessful,_tmpConfidence,_tmpProcessingTime,_tmpTimestamp,_tmpUserId,_tmpSessionId,_tmpContext,_tmpFollowUpRequired,_tmpFollowUpPrompt,_tmpErrorMessage,_tmpCorrectedCommand,_tmpActionTaken,_tmpResultData,_tmpUserFeedback,_tmpLanguage,_tmpAccent,_tmpNoiseLevel,_tmpDeviceType,_tmpIsOffline,_tmpRetryCount,_tmpAlternativeCommands,_tmpLearningData,_tmpPrivacyLevel,_tmpIsTrainingData,_tmpCustomVocabulary,_tmpShortcuts);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<VoiceCommand>> getVoiceCommandsByType(final String commandType) {
    final String _sql = "SELECT * FROM voice_commands WHERE commandType = ? ORDER BY timestamp DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (commandType == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, commandType);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"voice_commands"}, new Callable<List<VoiceCommand>>() {
      @Override
      @NonNull
      public List<VoiceCommand> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCommandText = CursorUtil.getColumnIndexOrThrow(_cursor, "commandText");
          final int _cursorIndexOfRecognizedText = CursorUtil.getColumnIndexOrThrow(_cursor, "recognizedText");
          final int _cursorIndexOfCommandType = CursorUtil.getColumnIndexOrThrow(_cursor, "commandType");
          final int _cursorIndexOfIntent = CursorUtil.getColumnIndexOrThrow(_cursor, "intent");
          final int _cursorIndexOfParameters = CursorUtil.getColumnIndexOrThrow(_cursor, "parameters");
          final int _cursorIndexOfIsSuccessful = CursorUtil.getColumnIndexOrThrow(_cursor, "isSuccessful");
          final int _cursorIndexOfConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "confidence");
          final int _cursorIndexOfProcessingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "processingTime");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfSessionId = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionId");
          final int _cursorIndexOfContext = CursorUtil.getColumnIndexOrThrow(_cursor, "context");
          final int _cursorIndexOfFollowUpRequired = CursorUtil.getColumnIndexOrThrow(_cursor, "followUpRequired");
          final int _cursorIndexOfFollowUpPrompt = CursorUtil.getColumnIndexOrThrow(_cursor, "followUpPrompt");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfCorrectedCommand = CursorUtil.getColumnIndexOrThrow(_cursor, "correctedCommand");
          final int _cursorIndexOfActionTaken = CursorUtil.getColumnIndexOrThrow(_cursor, "actionTaken");
          final int _cursorIndexOfResultData = CursorUtil.getColumnIndexOrThrow(_cursor, "resultData");
          final int _cursorIndexOfUserFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "userFeedback");
          final int _cursorIndexOfLanguage = CursorUtil.getColumnIndexOrThrow(_cursor, "language");
          final int _cursorIndexOfAccent = CursorUtil.getColumnIndexOrThrow(_cursor, "accent");
          final int _cursorIndexOfNoiseLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "noiseLevel");
          final int _cursorIndexOfDeviceType = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceType");
          final int _cursorIndexOfIsOffline = CursorUtil.getColumnIndexOrThrow(_cursor, "isOffline");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfAlternativeCommands = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeCommands");
          final int _cursorIndexOfLearningData = CursorUtil.getColumnIndexOrThrow(_cursor, "learningData");
          final int _cursorIndexOfPrivacyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "privacyLevel");
          final int _cursorIndexOfIsTrainingData = CursorUtil.getColumnIndexOrThrow(_cursor, "isTrainingData");
          final int _cursorIndexOfCustomVocabulary = CursorUtil.getColumnIndexOrThrow(_cursor, "customVocabulary");
          final int _cursorIndexOfShortcuts = CursorUtil.getColumnIndexOrThrow(_cursor, "shortcuts");
          final List<VoiceCommand> _result = new ArrayList<VoiceCommand>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final VoiceCommand _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCommandText;
            if (_cursor.isNull(_cursorIndexOfCommandText)) {
              _tmpCommandText = null;
            } else {
              _tmpCommandText = _cursor.getString(_cursorIndexOfCommandText);
            }
            final String _tmpRecognizedText;
            if (_cursor.isNull(_cursorIndexOfRecognizedText)) {
              _tmpRecognizedText = null;
            } else {
              _tmpRecognizedText = _cursor.getString(_cursorIndexOfRecognizedText);
            }
            final String _tmpCommandType;
            if (_cursor.isNull(_cursorIndexOfCommandType)) {
              _tmpCommandType = null;
            } else {
              _tmpCommandType = _cursor.getString(_cursorIndexOfCommandType);
            }
            final String _tmpIntent;
            if (_cursor.isNull(_cursorIndexOfIntent)) {
              _tmpIntent = null;
            } else {
              _tmpIntent = _cursor.getString(_cursorIndexOfIntent);
            }
            final String _tmpParameters;
            if (_cursor.isNull(_cursorIndexOfParameters)) {
              _tmpParameters = null;
            } else {
              _tmpParameters = _cursor.getString(_cursorIndexOfParameters);
            }
            final boolean _tmpIsSuccessful;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSuccessful);
            _tmpIsSuccessful = _tmp != 0;
            final double _tmpConfidence;
            _tmpConfidence = _cursor.getDouble(_cursorIndexOfConfidence);
            final long _tmpProcessingTime;
            _tmpProcessingTime = _cursor.getLong(_cursorIndexOfProcessingTime);
            final LocalDateTime _tmpTimestamp;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfTimestamp)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfTimestamp);
            }
            _tmpTimestamp = __converters.toLocalDateTime(_tmp_1);
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final String _tmpSessionId;
            if (_cursor.isNull(_cursorIndexOfSessionId)) {
              _tmpSessionId = null;
            } else {
              _tmpSessionId = _cursor.getString(_cursorIndexOfSessionId);
            }
            final String _tmpContext;
            if (_cursor.isNull(_cursorIndexOfContext)) {
              _tmpContext = null;
            } else {
              _tmpContext = _cursor.getString(_cursorIndexOfContext);
            }
            final boolean _tmpFollowUpRequired;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfFollowUpRequired);
            _tmpFollowUpRequired = _tmp_2 != 0;
            final String _tmpFollowUpPrompt;
            if (_cursor.isNull(_cursorIndexOfFollowUpPrompt)) {
              _tmpFollowUpPrompt = null;
            } else {
              _tmpFollowUpPrompt = _cursor.getString(_cursorIndexOfFollowUpPrompt);
            }
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final String _tmpCorrectedCommand;
            if (_cursor.isNull(_cursorIndexOfCorrectedCommand)) {
              _tmpCorrectedCommand = null;
            } else {
              _tmpCorrectedCommand = _cursor.getString(_cursorIndexOfCorrectedCommand);
            }
            final String _tmpActionTaken;
            if (_cursor.isNull(_cursorIndexOfActionTaken)) {
              _tmpActionTaken = null;
            } else {
              _tmpActionTaken = _cursor.getString(_cursorIndexOfActionTaken);
            }
            final String _tmpResultData;
            if (_cursor.isNull(_cursorIndexOfResultData)) {
              _tmpResultData = null;
            } else {
              _tmpResultData = _cursor.getString(_cursorIndexOfResultData);
            }
            final String _tmpUserFeedback;
            if (_cursor.isNull(_cursorIndexOfUserFeedback)) {
              _tmpUserFeedback = null;
            } else {
              _tmpUserFeedback = _cursor.getString(_cursorIndexOfUserFeedback);
            }
            final String _tmpLanguage;
            if (_cursor.isNull(_cursorIndexOfLanguage)) {
              _tmpLanguage = null;
            } else {
              _tmpLanguage = _cursor.getString(_cursorIndexOfLanguage);
            }
            final String _tmpAccent;
            if (_cursor.isNull(_cursorIndexOfAccent)) {
              _tmpAccent = null;
            } else {
              _tmpAccent = _cursor.getString(_cursorIndexOfAccent);
            }
            final String _tmpNoiseLevel;
            if (_cursor.isNull(_cursorIndexOfNoiseLevel)) {
              _tmpNoiseLevel = null;
            } else {
              _tmpNoiseLevel = _cursor.getString(_cursorIndexOfNoiseLevel);
            }
            final String _tmpDeviceType;
            if (_cursor.isNull(_cursorIndexOfDeviceType)) {
              _tmpDeviceType = null;
            } else {
              _tmpDeviceType = _cursor.getString(_cursorIndexOfDeviceType);
            }
            final boolean _tmpIsOffline;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsOffline);
            _tmpIsOffline = _tmp_3 != 0;
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final String _tmpAlternativeCommands;
            if (_cursor.isNull(_cursorIndexOfAlternativeCommands)) {
              _tmpAlternativeCommands = null;
            } else {
              _tmpAlternativeCommands = _cursor.getString(_cursorIndexOfAlternativeCommands);
            }
            final String _tmpLearningData;
            if (_cursor.isNull(_cursorIndexOfLearningData)) {
              _tmpLearningData = null;
            } else {
              _tmpLearningData = _cursor.getString(_cursorIndexOfLearningData);
            }
            final String _tmpPrivacyLevel;
            if (_cursor.isNull(_cursorIndexOfPrivacyLevel)) {
              _tmpPrivacyLevel = null;
            } else {
              _tmpPrivacyLevel = _cursor.getString(_cursorIndexOfPrivacyLevel);
            }
            final boolean _tmpIsTrainingData;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIsTrainingData);
            _tmpIsTrainingData = _tmp_4 != 0;
            final String _tmpCustomVocabulary;
            if (_cursor.isNull(_cursorIndexOfCustomVocabulary)) {
              _tmpCustomVocabulary = null;
            } else {
              _tmpCustomVocabulary = _cursor.getString(_cursorIndexOfCustomVocabulary);
            }
            final String _tmpShortcuts;
            if (_cursor.isNull(_cursorIndexOfShortcuts)) {
              _tmpShortcuts = null;
            } else {
              _tmpShortcuts = _cursor.getString(_cursorIndexOfShortcuts);
            }
            _item = new VoiceCommand(_tmpId,_tmpCommandText,_tmpRecognizedText,_tmpCommandType,_tmpIntent,_tmpParameters,_tmpIsSuccessful,_tmpConfidence,_tmpProcessingTime,_tmpTimestamp,_tmpUserId,_tmpSessionId,_tmpContext,_tmpFollowUpRequired,_tmpFollowUpPrompt,_tmpErrorMessage,_tmpCorrectedCommand,_tmpActionTaken,_tmpResultData,_tmpUserFeedback,_tmpLanguage,_tmpAccent,_tmpNoiseLevel,_tmpDeviceType,_tmpIsOffline,_tmpRetryCount,_tmpAlternativeCommands,_tmpLearningData,_tmpPrivacyLevel,_tmpIsTrainingData,_tmpCustomVocabulary,_tmpShortcuts);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<VoiceCommand>> getSuccessfulCommands() {
    final String _sql = "SELECT * FROM voice_commands WHERE isSuccessful = 1 ORDER BY timestamp DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"voice_commands"}, new Callable<List<VoiceCommand>>() {
      @Override
      @NonNull
      public List<VoiceCommand> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCommandText = CursorUtil.getColumnIndexOrThrow(_cursor, "commandText");
          final int _cursorIndexOfRecognizedText = CursorUtil.getColumnIndexOrThrow(_cursor, "recognizedText");
          final int _cursorIndexOfCommandType = CursorUtil.getColumnIndexOrThrow(_cursor, "commandType");
          final int _cursorIndexOfIntent = CursorUtil.getColumnIndexOrThrow(_cursor, "intent");
          final int _cursorIndexOfParameters = CursorUtil.getColumnIndexOrThrow(_cursor, "parameters");
          final int _cursorIndexOfIsSuccessful = CursorUtil.getColumnIndexOrThrow(_cursor, "isSuccessful");
          final int _cursorIndexOfConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "confidence");
          final int _cursorIndexOfProcessingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "processingTime");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfSessionId = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionId");
          final int _cursorIndexOfContext = CursorUtil.getColumnIndexOrThrow(_cursor, "context");
          final int _cursorIndexOfFollowUpRequired = CursorUtil.getColumnIndexOrThrow(_cursor, "followUpRequired");
          final int _cursorIndexOfFollowUpPrompt = CursorUtil.getColumnIndexOrThrow(_cursor, "followUpPrompt");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfCorrectedCommand = CursorUtil.getColumnIndexOrThrow(_cursor, "correctedCommand");
          final int _cursorIndexOfActionTaken = CursorUtil.getColumnIndexOrThrow(_cursor, "actionTaken");
          final int _cursorIndexOfResultData = CursorUtil.getColumnIndexOrThrow(_cursor, "resultData");
          final int _cursorIndexOfUserFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "userFeedback");
          final int _cursorIndexOfLanguage = CursorUtil.getColumnIndexOrThrow(_cursor, "language");
          final int _cursorIndexOfAccent = CursorUtil.getColumnIndexOrThrow(_cursor, "accent");
          final int _cursorIndexOfNoiseLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "noiseLevel");
          final int _cursorIndexOfDeviceType = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceType");
          final int _cursorIndexOfIsOffline = CursorUtil.getColumnIndexOrThrow(_cursor, "isOffline");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfAlternativeCommands = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeCommands");
          final int _cursorIndexOfLearningData = CursorUtil.getColumnIndexOrThrow(_cursor, "learningData");
          final int _cursorIndexOfPrivacyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "privacyLevel");
          final int _cursorIndexOfIsTrainingData = CursorUtil.getColumnIndexOrThrow(_cursor, "isTrainingData");
          final int _cursorIndexOfCustomVocabulary = CursorUtil.getColumnIndexOrThrow(_cursor, "customVocabulary");
          final int _cursorIndexOfShortcuts = CursorUtil.getColumnIndexOrThrow(_cursor, "shortcuts");
          final List<VoiceCommand> _result = new ArrayList<VoiceCommand>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final VoiceCommand _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCommandText;
            if (_cursor.isNull(_cursorIndexOfCommandText)) {
              _tmpCommandText = null;
            } else {
              _tmpCommandText = _cursor.getString(_cursorIndexOfCommandText);
            }
            final String _tmpRecognizedText;
            if (_cursor.isNull(_cursorIndexOfRecognizedText)) {
              _tmpRecognizedText = null;
            } else {
              _tmpRecognizedText = _cursor.getString(_cursorIndexOfRecognizedText);
            }
            final String _tmpCommandType;
            if (_cursor.isNull(_cursorIndexOfCommandType)) {
              _tmpCommandType = null;
            } else {
              _tmpCommandType = _cursor.getString(_cursorIndexOfCommandType);
            }
            final String _tmpIntent;
            if (_cursor.isNull(_cursorIndexOfIntent)) {
              _tmpIntent = null;
            } else {
              _tmpIntent = _cursor.getString(_cursorIndexOfIntent);
            }
            final String _tmpParameters;
            if (_cursor.isNull(_cursorIndexOfParameters)) {
              _tmpParameters = null;
            } else {
              _tmpParameters = _cursor.getString(_cursorIndexOfParameters);
            }
            final boolean _tmpIsSuccessful;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSuccessful);
            _tmpIsSuccessful = _tmp != 0;
            final double _tmpConfidence;
            _tmpConfidence = _cursor.getDouble(_cursorIndexOfConfidence);
            final long _tmpProcessingTime;
            _tmpProcessingTime = _cursor.getLong(_cursorIndexOfProcessingTime);
            final LocalDateTime _tmpTimestamp;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfTimestamp)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfTimestamp);
            }
            _tmpTimestamp = __converters.toLocalDateTime(_tmp_1);
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final String _tmpSessionId;
            if (_cursor.isNull(_cursorIndexOfSessionId)) {
              _tmpSessionId = null;
            } else {
              _tmpSessionId = _cursor.getString(_cursorIndexOfSessionId);
            }
            final String _tmpContext;
            if (_cursor.isNull(_cursorIndexOfContext)) {
              _tmpContext = null;
            } else {
              _tmpContext = _cursor.getString(_cursorIndexOfContext);
            }
            final boolean _tmpFollowUpRequired;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfFollowUpRequired);
            _tmpFollowUpRequired = _tmp_2 != 0;
            final String _tmpFollowUpPrompt;
            if (_cursor.isNull(_cursorIndexOfFollowUpPrompt)) {
              _tmpFollowUpPrompt = null;
            } else {
              _tmpFollowUpPrompt = _cursor.getString(_cursorIndexOfFollowUpPrompt);
            }
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final String _tmpCorrectedCommand;
            if (_cursor.isNull(_cursorIndexOfCorrectedCommand)) {
              _tmpCorrectedCommand = null;
            } else {
              _tmpCorrectedCommand = _cursor.getString(_cursorIndexOfCorrectedCommand);
            }
            final String _tmpActionTaken;
            if (_cursor.isNull(_cursorIndexOfActionTaken)) {
              _tmpActionTaken = null;
            } else {
              _tmpActionTaken = _cursor.getString(_cursorIndexOfActionTaken);
            }
            final String _tmpResultData;
            if (_cursor.isNull(_cursorIndexOfResultData)) {
              _tmpResultData = null;
            } else {
              _tmpResultData = _cursor.getString(_cursorIndexOfResultData);
            }
            final String _tmpUserFeedback;
            if (_cursor.isNull(_cursorIndexOfUserFeedback)) {
              _tmpUserFeedback = null;
            } else {
              _tmpUserFeedback = _cursor.getString(_cursorIndexOfUserFeedback);
            }
            final String _tmpLanguage;
            if (_cursor.isNull(_cursorIndexOfLanguage)) {
              _tmpLanguage = null;
            } else {
              _tmpLanguage = _cursor.getString(_cursorIndexOfLanguage);
            }
            final String _tmpAccent;
            if (_cursor.isNull(_cursorIndexOfAccent)) {
              _tmpAccent = null;
            } else {
              _tmpAccent = _cursor.getString(_cursorIndexOfAccent);
            }
            final String _tmpNoiseLevel;
            if (_cursor.isNull(_cursorIndexOfNoiseLevel)) {
              _tmpNoiseLevel = null;
            } else {
              _tmpNoiseLevel = _cursor.getString(_cursorIndexOfNoiseLevel);
            }
            final String _tmpDeviceType;
            if (_cursor.isNull(_cursorIndexOfDeviceType)) {
              _tmpDeviceType = null;
            } else {
              _tmpDeviceType = _cursor.getString(_cursorIndexOfDeviceType);
            }
            final boolean _tmpIsOffline;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsOffline);
            _tmpIsOffline = _tmp_3 != 0;
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final String _tmpAlternativeCommands;
            if (_cursor.isNull(_cursorIndexOfAlternativeCommands)) {
              _tmpAlternativeCommands = null;
            } else {
              _tmpAlternativeCommands = _cursor.getString(_cursorIndexOfAlternativeCommands);
            }
            final String _tmpLearningData;
            if (_cursor.isNull(_cursorIndexOfLearningData)) {
              _tmpLearningData = null;
            } else {
              _tmpLearningData = _cursor.getString(_cursorIndexOfLearningData);
            }
            final String _tmpPrivacyLevel;
            if (_cursor.isNull(_cursorIndexOfPrivacyLevel)) {
              _tmpPrivacyLevel = null;
            } else {
              _tmpPrivacyLevel = _cursor.getString(_cursorIndexOfPrivacyLevel);
            }
            final boolean _tmpIsTrainingData;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIsTrainingData);
            _tmpIsTrainingData = _tmp_4 != 0;
            final String _tmpCustomVocabulary;
            if (_cursor.isNull(_cursorIndexOfCustomVocabulary)) {
              _tmpCustomVocabulary = null;
            } else {
              _tmpCustomVocabulary = _cursor.getString(_cursorIndexOfCustomVocabulary);
            }
            final String _tmpShortcuts;
            if (_cursor.isNull(_cursorIndexOfShortcuts)) {
              _tmpShortcuts = null;
            } else {
              _tmpShortcuts = _cursor.getString(_cursorIndexOfShortcuts);
            }
            _item = new VoiceCommand(_tmpId,_tmpCommandText,_tmpRecognizedText,_tmpCommandType,_tmpIntent,_tmpParameters,_tmpIsSuccessful,_tmpConfidence,_tmpProcessingTime,_tmpTimestamp,_tmpUserId,_tmpSessionId,_tmpContext,_tmpFollowUpRequired,_tmpFollowUpPrompt,_tmpErrorMessage,_tmpCorrectedCommand,_tmpActionTaken,_tmpResultData,_tmpUserFeedback,_tmpLanguage,_tmpAccent,_tmpNoiseLevel,_tmpDeviceType,_tmpIsOffline,_tmpRetryCount,_tmpAlternativeCommands,_tmpLearningData,_tmpPrivacyLevel,_tmpIsTrainingData,_tmpCustomVocabulary,_tmpShortcuts);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<VoiceCommand>> getRecentCommands(final LocalDateTime startDate) {
    final String _sql = "SELECT * FROM voice_commands WHERE timestamp >= ? ORDER BY timestamp DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final String _tmp = __converters.fromLocalDateTime(startDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"voice_commands"}, new Callable<List<VoiceCommand>>() {
      @Override
      @NonNull
      public List<VoiceCommand> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCommandText = CursorUtil.getColumnIndexOrThrow(_cursor, "commandText");
          final int _cursorIndexOfRecognizedText = CursorUtil.getColumnIndexOrThrow(_cursor, "recognizedText");
          final int _cursorIndexOfCommandType = CursorUtil.getColumnIndexOrThrow(_cursor, "commandType");
          final int _cursorIndexOfIntent = CursorUtil.getColumnIndexOrThrow(_cursor, "intent");
          final int _cursorIndexOfParameters = CursorUtil.getColumnIndexOrThrow(_cursor, "parameters");
          final int _cursorIndexOfIsSuccessful = CursorUtil.getColumnIndexOrThrow(_cursor, "isSuccessful");
          final int _cursorIndexOfConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "confidence");
          final int _cursorIndexOfProcessingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "processingTime");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfSessionId = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionId");
          final int _cursorIndexOfContext = CursorUtil.getColumnIndexOrThrow(_cursor, "context");
          final int _cursorIndexOfFollowUpRequired = CursorUtil.getColumnIndexOrThrow(_cursor, "followUpRequired");
          final int _cursorIndexOfFollowUpPrompt = CursorUtil.getColumnIndexOrThrow(_cursor, "followUpPrompt");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfCorrectedCommand = CursorUtil.getColumnIndexOrThrow(_cursor, "correctedCommand");
          final int _cursorIndexOfActionTaken = CursorUtil.getColumnIndexOrThrow(_cursor, "actionTaken");
          final int _cursorIndexOfResultData = CursorUtil.getColumnIndexOrThrow(_cursor, "resultData");
          final int _cursorIndexOfUserFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "userFeedback");
          final int _cursorIndexOfLanguage = CursorUtil.getColumnIndexOrThrow(_cursor, "language");
          final int _cursorIndexOfAccent = CursorUtil.getColumnIndexOrThrow(_cursor, "accent");
          final int _cursorIndexOfNoiseLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "noiseLevel");
          final int _cursorIndexOfDeviceType = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceType");
          final int _cursorIndexOfIsOffline = CursorUtil.getColumnIndexOrThrow(_cursor, "isOffline");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfAlternativeCommands = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeCommands");
          final int _cursorIndexOfLearningData = CursorUtil.getColumnIndexOrThrow(_cursor, "learningData");
          final int _cursorIndexOfPrivacyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "privacyLevel");
          final int _cursorIndexOfIsTrainingData = CursorUtil.getColumnIndexOrThrow(_cursor, "isTrainingData");
          final int _cursorIndexOfCustomVocabulary = CursorUtil.getColumnIndexOrThrow(_cursor, "customVocabulary");
          final int _cursorIndexOfShortcuts = CursorUtil.getColumnIndexOrThrow(_cursor, "shortcuts");
          final List<VoiceCommand> _result = new ArrayList<VoiceCommand>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final VoiceCommand _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCommandText;
            if (_cursor.isNull(_cursorIndexOfCommandText)) {
              _tmpCommandText = null;
            } else {
              _tmpCommandText = _cursor.getString(_cursorIndexOfCommandText);
            }
            final String _tmpRecognizedText;
            if (_cursor.isNull(_cursorIndexOfRecognizedText)) {
              _tmpRecognizedText = null;
            } else {
              _tmpRecognizedText = _cursor.getString(_cursorIndexOfRecognizedText);
            }
            final String _tmpCommandType;
            if (_cursor.isNull(_cursorIndexOfCommandType)) {
              _tmpCommandType = null;
            } else {
              _tmpCommandType = _cursor.getString(_cursorIndexOfCommandType);
            }
            final String _tmpIntent;
            if (_cursor.isNull(_cursorIndexOfIntent)) {
              _tmpIntent = null;
            } else {
              _tmpIntent = _cursor.getString(_cursorIndexOfIntent);
            }
            final String _tmpParameters;
            if (_cursor.isNull(_cursorIndexOfParameters)) {
              _tmpParameters = null;
            } else {
              _tmpParameters = _cursor.getString(_cursorIndexOfParameters);
            }
            final boolean _tmpIsSuccessful;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsSuccessful);
            _tmpIsSuccessful = _tmp_1 != 0;
            final double _tmpConfidence;
            _tmpConfidence = _cursor.getDouble(_cursorIndexOfConfidence);
            final long _tmpProcessingTime;
            _tmpProcessingTime = _cursor.getLong(_cursorIndexOfProcessingTime);
            final LocalDateTime _tmpTimestamp;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfTimestamp)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfTimestamp);
            }
            _tmpTimestamp = __converters.toLocalDateTime(_tmp_2);
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final String _tmpSessionId;
            if (_cursor.isNull(_cursorIndexOfSessionId)) {
              _tmpSessionId = null;
            } else {
              _tmpSessionId = _cursor.getString(_cursorIndexOfSessionId);
            }
            final String _tmpContext;
            if (_cursor.isNull(_cursorIndexOfContext)) {
              _tmpContext = null;
            } else {
              _tmpContext = _cursor.getString(_cursorIndexOfContext);
            }
            final boolean _tmpFollowUpRequired;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfFollowUpRequired);
            _tmpFollowUpRequired = _tmp_3 != 0;
            final String _tmpFollowUpPrompt;
            if (_cursor.isNull(_cursorIndexOfFollowUpPrompt)) {
              _tmpFollowUpPrompt = null;
            } else {
              _tmpFollowUpPrompt = _cursor.getString(_cursorIndexOfFollowUpPrompt);
            }
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final String _tmpCorrectedCommand;
            if (_cursor.isNull(_cursorIndexOfCorrectedCommand)) {
              _tmpCorrectedCommand = null;
            } else {
              _tmpCorrectedCommand = _cursor.getString(_cursorIndexOfCorrectedCommand);
            }
            final String _tmpActionTaken;
            if (_cursor.isNull(_cursorIndexOfActionTaken)) {
              _tmpActionTaken = null;
            } else {
              _tmpActionTaken = _cursor.getString(_cursorIndexOfActionTaken);
            }
            final String _tmpResultData;
            if (_cursor.isNull(_cursorIndexOfResultData)) {
              _tmpResultData = null;
            } else {
              _tmpResultData = _cursor.getString(_cursorIndexOfResultData);
            }
            final String _tmpUserFeedback;
            if (_cursor.isNull(_cursorIndexOfUserFeedback)) {
              _tmpUserFeedback = null;
            } else {
              _tmpUserFeedback = _cursor.getString(_cursorIndexOfUserFeedback);
            }
            final String _tmpLanguage;
            if (_cursor.isNull(_cursorIndexOfLanguage)) {
              _tmpLanguage = null;
            } else {
              _tmpLanguage = _cursor.getString(_cursorIndexOfLanguage);
            }
            final String _tmpAccent;
            if (_cursor.isNull(_cursorIndexOfAccent)) {
              _tmpAccent = null;
            } else {
              _tmpAccent = _cursor.getString(_cursorIndexOfAccent);
            }
            final String _tmpNoiseLevel;
            if (_cursor.isNull(_cursorIndexOfNoiseLevel)) {
              _tmpNoiseLevel = null;
            } else {
              _tmpNoiseLevel = _cursor.getString(_cursorIndexOfNoiseLevel);
            }
            final String _tmpDeviceType;
            if (_cursor.isNull(_cursorIndexOfDeviceType)) {
              _tmpDeviceType = null;
            } else {
              _tmpDeviceType = _cursor.getString(_cursorIndexOfDeviceType);
            }
            final boolean _tmpIsOffline;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIsOffline);
            _tmpIsOffline = _tmp_4 != 0;
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final String _tmpAlternativeCommands;
            if (_cursor.isNull(_cursorIndexOfAlternativeCommands)) {
              _tmpAlternativeCommands = null;
            } else {
              _tmpAlternativeCommands = _cursor.getString(_cursorIndexOfAlternativeCommands);
            }
            final String _tmpLearningData;
            if (_cursor.isNull(_cursorIndexOfLearningData)) {
              _tmpLearningData = null;
            } else {
              _tmpLearningData = _cursor.getString(_cursorIndexOfLearningData);
            }
            final String _tmpPrivacyLevel;
            if (_cursor.isNull(_cursorIndexOfPrivacyLevel)) {
              _tmpPrivacyLevel = null;
            } else {
              _tmpPrivacyLevel = _cursor.getString(_cursorIndexOfPrivacyLevel);
            }
            final boolean _tmpIsTrainingData;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsTrainingData);
            _tmpIsTrainingData = _tmp_5 != 0;
            final String _tmpCustomVocabulary;
            if (_cursor.isNull(_cursorIndexOfCustomVocabulary)) {
              _tmpCustomVocabulary = null;
            } else {
              _tmpCustomVocabulary = _cursor.getString(_cursorIndexOfCustomVocabulary);
            }
            final String _tmpShortcuts;
            if (_cursor.isNull(_cursorIndexOfShortcuts)) {
              _tmpShortcuts = null;
            } else {
              _tmpShortcuts = _cursor.getString(_cursorIndexOfShortcuts);
            }
            _item = new VoiceCommand(_tmpId,_tmpCommandText,_tmpRecognizedText,_tmpCommandType,_tmpIntent,_tmpParameters,_tmpIsSuccessful,_tmpConfidence,_tmpProcessingTime,_tmpTimestamp,_tmpUserId,_tmpSessionId,_tmpContext,_tmpFollowUpRequired,_tmpFollowUpPrompt,_tmpErrorMessage,_tmpCorrectedCommand,_tmpActionTaken,_tmpResultData,_tmpUserFeedback,_tmpLanguage,_tmpAccent,_tmpNoiseLevel,_tmpDeviceType,_tmpIsOffline,_tmpRetryCount,_tmpAlternativeCommands,_tmpLearningData,_tmpPrivacyLevel,_tmpIsTrainingData,_tmpCustomVocabulary,_tmpShortcuts);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getVoiceCommandById(final long id,
      final Continuation<? super VoiceCommand> $completion) {
    final String _sql = "SELECT * FROM voice_commands WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<VoiceCommand>() {
      @Override
      @Nullable
      public VoiceCommand call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfCommandText = CursorUtil.getColumnIndexOrThrow(_cursor, "commandText");
          final int _cursorIndexOfRecognizedText = CursorUtil.getColumnIndexOrThrow(_cursor, "recognizedText");
          final int _cursorIndexOfCommandType = CursorUtil.getColumnIndexOrThrow(_cursor, "commandType");
          final int _cursorIndexOfIntent = CursorUtil.getColumnIndexOrThrow(_cursor, "intent");
          final int _cursorIndexOfParameters = CursorUtil.getColumnIndexOrThrow(_cursor, "parameters");
          final int _cursorIndexOfIsSuccessful = CursorUtil.getColumnIndexOrThrow(_cursor, "isSuccessful");
          final int _cursorIndexOfConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "confidence");
          final int _cursorIndexOfProcessingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "processingTime");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfSessionId = CursorUtil.getColumnIndexOrThrow(_cursor, "sessionId");
          final int _cursorIndexOfContext = CursorUtil.getColumnIndexOrThrow(_cursor, "context");
          final int _cursorIndexOfFollowUpRequired = CursorUtil.getColumnIndexOrThrow(_cursor, "followUpRequired");
          final int _cursorIndexOfFollowUpPrompt = CursorUtil.getColumnIndexOrThrow(_cursor, "followUpPrompt");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfCorrectedCommand = CursorUtil.getColumnIndexOrThrow(_cursor, "correctedCommand");
          final int _cursorIndexOfActionTaken = CursorUtil.getColumnIndexOrThrow(_cursor, "actionTaken");
          final int _cursorIndexOfResultData = CursorUtil.getColumnIndexOrThrow(_cursor, "resultData");
          final int _cursorIndexOfUserFeedback = CursorUtil.getColumnIndexOrThrow(_cursor, "userFeedback");
          final int _cursorIndexOfLanguage = CursorUtil.getColumnIndexOrThrow(_cursor, "language");
          final int _cursorIndexOfAccent = CursorUtil.getColumnIndexOrThrow(_cursor, "accent");
          final int _cursorIndexOfNoiseLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "noiseLevel");
          final int _cursorIndexOfDeviceType = CursorUtil.getColumnIndexOrThrow(_cursor, "deviceType");
          final int _cursorIndexOfIsOffline = CursorUtil.getColumnIndexOrThrow(_cursor, "isOffline");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfAlternativeCommands = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeCommands");
          final int _cursorIndexOfLearningData = CursorUtil.getColumnIndexOrThrow(_cursor, "learningData");
          final int _cursorIndexOfPrivacyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "privacyLevel");
          final int _cursorIndexOfIsTrainingData = CursorUtil.getColumnIndexOrThrow(_cursor, "isTrainingData");
          final int _cursorIndexOfCustomVocabulary = CursorUtil.getColumnIndexOrThrow(_cursor, "customVocabulary");
          final int _cursorIndexOfShortcuts = CursorUtil.getColumnIndexOrThrow(_cursor, "shortcuts");
          final VoiceCommand _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpCommandText;
            if (_cursor.isNull(_cursorIndexOfCommandText)) {
              _tmpCommandText = null;
            } else {
              _tmpCommandText = _cursor.getString(_cursorIndexOfCommandText);
            }
            final String _tmpRecognizedText;
            if (_cursor.isNull(_cursorIndexOfRecognizedText)) {
              _tmpRecognizedText = null;
            } else {
              _tmpRecognizedText = _cursor.getString(_cursorIndexOfRecognizedText);
            }
            final String _tmpCommandType;
            if (_cursor.isNull(_cursorIndexOfCommandType)) {
              _tmpCommandType = null;
            } else {
              _tmpCommandType = _cursor.getString(_cursorIndexOfCommandType);
            }
            final String _tmpIntent;
            if (_cursor.isNull(_cursorIndexOfIntent)) {
              _tmpIntent = null;
            } else {
              _tmpIntent = _cursor.getString(_cursorIndexOfIntent);
            }
            final String _tmpParameters;
            if (_cursor.isNull(_cursorIndexOfParameters)) {
              _tmpParameters = null;
            } else {
              _tmpParameters = _cursor.getString(_cursorIndexOfParameters);
            }
            final boolean _tmpIsSuccessful;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSuccessful);
            _tmpIsSuccessful = _tmp != 0;
            final double _tmpConfidence;
            _tmpConfidence = _cursor.getDouble(_cursorIndexOfConfidence);
            final long _tmpProcessingTime;
            _tmpProcessingTime = _cursor.getLong(_cursorIndexOfProcessingTime);
            final LocalDateTime _tmpTimestamp;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfTimestamp)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfTimestamp);
            }
            _tmpTimestamp = __converters.toLocalDateTime(_tmp_1);
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final String _tmpSessionId;
            if (_cursor.isNull(_cursorIndexOfSessionId)) {
              _tmpSessionId = null;
            } else {
              _tmpSessionId = _cursor.getString(_cursorIndexOfSessionId);
            }
            final String _tmpContext;
            if (_cursor.isNull(_cursorIndexOfContext)) {
              _tmpContext = null;
            } else {
              _tmpContext = _cursor.getString(_cursorIndexOfContext);
            }
            final boolean _tmpFollowUpRequired;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfFollowUpRequired);
            _tmpFollowUpRequired = _tmp_2 != 0;
            final String _tmpFollowUpPrompt;
            if (_cursor.isNull(_cursorIndexOfFollowUpPrompt)) {
              _tmpFollowUpPrompt = null;
            } else {
              _tmpFollowUpPrompt = _cursor.getString(_cursorIndexOfFollowUpPrompt);
            }
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final String _tmpCorrectedCommand;
            if (_cursor.isNull(_cursorIndexOfCorrectedCommand)) {
              _tmpCorrectedCommand = null;
            } else {
              _tmpCorrectedCommand = _cursor.getString(_cursorIndexOfCorrectedCommand);
            }
            final String _tmpActionTaken;
            if (_cursor.isNull(_cursorIndexOfActionTaken)) {
              _tmpActionTaken = null;
            } else {
              _tmpActionTaken = _cursor.getString(_cursorIndexOfActionTaken);
            }
            final String _tmpResultData;
            if (_cursor.isNull(_cursorIndexOfResultData)) {
              _tmpResultData = null;
            } else {
              _tmpResultData = _cursor.getString(_cursorIndexOfResultData);
            }
            final String _tmpUserFeedback;
            if (_cursor.isNull(_cursorIndexOfUserFeedback)) {
              _tmpUserFeedback = null;
            } else {
              _tmpUserFeedback = _cursor.getString(_cursorIndexOfUserFeedback);
            }
            final String _tmpLanguage;
            if (_cursor.isNull(_cursorIndexOfLanguage)) {
              _tmpLanguage = null;
            } else {
              _tmpLanguage = _cursor.getString(_cursorIndexOfLanguage);
            }
            final String _tmpAccent;
            if (_cursor.isNull(_cursorIndexOfAccent)) {
              _tmpAccent = null;
            } else {
              _tmpAccent = _cursor.getString(_cursorIndexOfAccent);
            }
            final String _tmpNoiseLevel;
            if (_cursor.isNull(_cursorIndexOfNoiseLevel)) {
              _tmpNoiseLevel = null;
            } else {
              _tmpNoiseLevel = _cursor.getString(_cursorIndexOfNoiseLevel);
            }
            final String _tmpDeviceType;
            if (_cursor.isNull(_cursorIndexOfDeviceType)) {
              _tmpDeviceType = null;
            } else {
              _tmpDeviceType = _cursor.getString(_cursorIndexOfDeviceType);
            }
            final boolean _tmpIsOffline;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsOffline);
            _tmpIsOffline = _tmp_3 != 0;
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final String _tmpAlternativeCommands;
            if (_cursor.isNull(_cursorIndexOfAlternativeCommands)) {
              _tmpAlternativeCommands = null;
            } else {
              _tmpAlternativeCommands = _cursor.getString(_cursorIndexOfAlternativeCommands);
            }
            final String _tmpLearningData;
            if (_cursor.isNull(_cursorIndexOfLearningData)) {
              _tmpLearningData = null;
            } else {
              _tmpLearningData = _cursor.getString(_cursorIndexOfLearningData);
            }
            final String _tmpPrivacyLevel;
            if (_cursor.isNull(_cursorIndexOfPrivacyLevel)) {
              _tmpPrivacyLevel = null;
            } else {
              _tmpPrivacyLevel = _cursor.getString(_cursorIndexOfPrivacyLevel);
            }
            final boolean _tmpIsTrainingData;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIsTrainingData);
            _tmpIsTrainingData = _tmp_4 != 0;
            final String _tmpCustomVocabulary;
            if (_cursor.isNull(_cursorIndexOfCustomVocabulary)) {
              _tmpCustomVocabulary = null;
            } else {
              _tmpCustomVocabulary = _cursor.getString(_cursorIndexOfCustomVocabulary);
            }
            final String _tmpShortcuts;
            if (_cursor.isNull(_cursorIndexOfShortcuts)) {
              _tmpShortcuts = null;
            } else {
              _tmpShortcuts = _cursor.getString(_cursorIndexOfShortcuts);
            }
            _result = new VoiceCommand(_tmpId,_tmpCommandText,_tmpRecognizedText,_tmpCommandType,_tmpIntent,_tmpParameters,_tmpIsSuccessful,_tmpConfidence,_tmpProcessingTime,_tmpTimestamp,_tmpUserId,_tmpSessionId,_tmpContext,_tmpFollowUpRequired,_tmpFollowUpPrompt,_tmpErrorMessage,_tmpCorrectedCommand,_tmpActionTaken,_tmpResultData,_tmpUserFeedback,_tmpLanguage,_tmpAccent,_tmpNoiseLevel,_tmpDeviceType,_tmpIsOffline,_tmpRetryCount,_tmpAlternativeCommands,_tmpLearningData,_tmpPrivacyLevel,_tmpIsTrainingData,_tmpCustomVocabulary,_tmpShortcuts);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getSuccessfulCommandCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM voice_commands WHERE isSuccessful = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalCommandCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM voice_commands";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAverageSuccessConfidence(final Continuation<? super Double> $completion) {
    final String _sql = "SELECT AVG(confidence) FROM voice_commands WHERE isSuccessful = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAverageProcessingTime(final Continuation<? super Double> $completion) {
    final String _sql = "SELECT AVG(processingTime) FROM voice_commands";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCommandTypeUsage(
      final Continuation<? super List<CommandTypeUsage>> $completion) {
    final String _sql = "SELECT commandType, COUNT(*) as count FROM voice_commands WHERE isSuccessful = 1 GROUP BY commandType ORDER BY count DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CommandTypeUsage>>() {
      @Override
      @NonNull
      public List<CommandTypeUsage> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCommandType = 0;
          final int _cursorIndexOfCount = 1;
          final List<CommandTypeUsage> _result = new ArrayList<CommandTypeUsage>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CommandTypeUsage _item;
            final String _tmpCommandType;
            if (_cursor.isNull(_cursorIndexOfCommandType)) {
              _tmpCommandType = null;
            } else {
              _tmpCommandType = _cursor.getString(_cursorIndexOfCommandType);
            }
            final int _tmpCount;
            _tmpCount = _cursor.getInt(_cursorIndexOfCount);
            _item = new CommandTypeUsage(_tmpCommandType,_tmpCount);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
