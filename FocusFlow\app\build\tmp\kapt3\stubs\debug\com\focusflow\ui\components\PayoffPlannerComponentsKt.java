package com.focusflow.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000d\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\u001aL\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\u001c\u0010\b\u001a\u0018\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00010\t\u00a2\u0006\u0002\b\u000b\u00a2\u0006\u0002\b\fH\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\r\u0010\u000e\u001a&\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00112\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00010\u0014H\u0007\u001a2\u0010\u0015\u001a\u00020\u00012\u0006\u0010\u0016\u001a\u00020\u00172\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00010\u00142\u0012\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\tH\u0007\u001a\u0010\u0010\u001a\u001a\u00020\u00012\u0006\u0010\u001b\u001a\u00020\u001cH\u0007\u001a2\u0010\u001d\u001a\u00020\u00012\u0006\u0010\u001e\u001a\u00020\u00032\u0006\u0010\u001f\u001a\u00020\u00032\u0006\u0010 \u001a\u00020!2\u0006\u0010\"\u001a\u00020\u0007H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b#\u0010$\u001aL\u0010%\u001a\u00020\u00012\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010&\u001a\u00020\'2\u0006\u0010(\u001a\u00020)2\u0012\u0010*\u001a\u000e\u0012\u0004\u0012\u00020\u0017\u0012\u0004\u0012\u00020\u00010\t2\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010+\u001a\u00020\u00032\u0006\u0010 \u001a\u00020!H\u0007\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006,"}, d2 = {"ADHDFriendlyCard", "", "title", "", "modifier", "Landroidx/compose/ui/Modifier;", "backgroundColor", "Landroidx/compose/ui/graphics/Color;", "content", "Lkotlin/Function1;", "Landroidx/compose/foundation/layout/ColumnScope;", "Landroidx/compose/runtime/Composable;", "Lkotlin/ExtensionFunctionType;", "ADHDFriendlyCard-9LQNqLg", "(Ljava/lang/String;Landroidx/compose/ui/Modifier;JLkotlin/jvm/functions/Function1;)V", "BudgetIntegrationCard", "availableAmount", "", "recommendedAmount", "onUseRecommended", "Lkotlin/Function0;", "CreatePlanDialog", "strategy", "Lcom/focusflow/ui/viewmodel/PayoffStrategy;", "onDismiss", "onCreatePlan", "CurrentPlanCard", "plan", "Lcom/focusflow/data/model/PayoffPlan;", "StrategyMetric", "label", "value", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "color", "StrategyMetric-g2O1Hgs", "(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/graphics/vector/ImageVector;J)V", "StrategyOptionCard", "comparison", "Lcom/focusflow/data/model/PayoffComparison;", "isSelected", "", "onSelected", "description", "app_debug"})
public final class PayoffPlannerComponentsKt {
    
    @androidx.compose.runtime.Composable
    public static final void StrategyOptionCard(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffStrategy strategy, @org.jetbrains.annotations.NotNull
    com.focusflow.data.model.PayoffComparison comparison, boolean isSelected, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.ui.viewmodel.PayoffStrategy, kotlin.Unit> onSelected, @org.jetbrains.annotations.NotNull
    java.lang.String title, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.graphics.vector.ImageVector icon) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void CreatePlanDialog(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffStrategy strategy, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onCreatePlan) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void CurrentPlanCard(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.PayoffPlan plan) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void BudgetIntegrationCard(double availableAmount, double recommendedAmount, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onUseRecommended) {
    }
}