{"logs": [{"outputFile": "com.focusflow.app-mergeReleaseResources-66:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0e1dc919567705f737931e90f7aead7b\\transformed\\biometric-1.1.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,263,385,515,648,784,906,1069,1170,1304,1441", "endColumns": "113,93,121,129,132,135,121,162,100,133,136,130", "endOffsets": "164,258,380,510,643,779,901,1064,1165,1299,1436,1567"}, "to": {"startLines": "38,40,43,44,45,46,47,48,49,50,51,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3791,4008,4289,4411,4541,4674,4810,4932,5095,5196,5330,5467", "endColumns": "113,93,121,129,132,135,121,162,100,133,136,130", "endOffsets": "3900,4097,4406,4536,4669,4805,4927,5090,5191,5325,5462,5593"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1f170a70d96fcd26f910cf5fef71c6ab\\transformed\\jetified-ui-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,301,404,507,591,667,758,849,933,1000,1066,1150,1238,1310,1393,1462", "endColumns": "102,92,102,102,83,75,90,90,83,66,65,83,87,71,82,68,120", "endOffsets": "203,296,399,502,586,662,753,844,928,995,1061,1145,1233,1305,1388,1457,1578"}, "to": {"startLines": "36,37,39,41,42,53,54,55,56,57,58,59,60,62,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3595,3698,3905,4102,4205,5598,5674,5765,5856,5940,6007,6073,6157,6329,6502,6585,6654", "endColumns": "102,92,102,102,83,75,90,90,83,66,65,83,87,71,82,68,120", "endOffsets": "3693,3786,4003,4200,4284,5669,5760,5851,5935,6002,6068,6152,6240,6396,6580,6649,6770"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b7575953e232ac886e0021593ed04f20\\transformed\\appcompat-1.6.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,6245", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,6324"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\904fd36595e0135e8fdca5c15906c24d\\transformed\\core-1.12.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2854,2951,3061,3163,3264,3371,3476,6401", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "2946,3056,3158,3259,3366,3471,3590,6497"}}]}]}