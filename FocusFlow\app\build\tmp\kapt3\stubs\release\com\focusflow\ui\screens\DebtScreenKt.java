package com.focusflow.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000P\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0004\u001aH\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u000320\u0010\u0004\u001a,\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a:\u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000b2\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a4\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\u00072\u0006\u0010\u0012\u001a\u00020\u00132\b\b\u0002\u0010\u0014\u001a\u00020\u0015H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0016\u0010\u0017\u001a\"\u0010\u0018\u001a\u00020\u00012\b\b\u0002\u0010\u0019\u001a\u00020\u001a2\u000e\b\u0002\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a\b\u0010\u001c\u001a\u00020\u0001H\u0007\u001a2\u0010\u001d\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000b2\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u0012\u0010\u001e\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u001fH\u0007\u001a:\u0010 \u001a\u00020\u00012\f\u0010!\u001a\b\u0012\u0004\u0012\u00020\u000b0\"2\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u0006\u0010\u0019\u001a\u00020\u001aH\u0007\u001a\u0010\u0010$\u001a\u00020\u00062\u0006\u0010%\u001a\u00020\bH\u0002\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006&"}, d2 = {"AddCreditCardDialog", "", "onDismiss", "Lkotlin/Function0;", "onAddCard", "Lkotlin/Function6;", "", "", "Lkotlinx/datetime/LocalDate;", "CreditCardItem", "creditCard", "Lcom/focusflow/data/model/CreditCard;", "onPaymentClick", "onEditClick", "onDeleteClick", "DebtOverviewCard", "title", "amount", "color", "Landroidx/compose/ui/graphics/Color;", "modifier", "Landroidx/compose/ui/Modifier;", "DebtOverviewCard-9LQNqLg", "(Ljava/lang/String;DJLandroidx/compose/ui/Modifier;)V", "DebtScreen", "viewModel", "Lcom/focusflow/ui/viewmodel/DebtViewModel;", "onNavigateToPayoffPlanner", "EmptyStateCard", "PaymentDialog", "onMakePayment", "Lkotlin/Function1;", "PayoffPlannerDialog", "creditCards", "", "onNavigateToPlanner", "formatDate", "date", "app_release"})
public final class DebtScreenKt {
    
    @androidx.compose.runtime.Composable
    public static final void DebtScreen(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.DebtViewModel viewModel, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToPayoffPlanner) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void CreditCardItem(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.CreditCard creditCard, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onPaymentClick, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onEditClick, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDeleteClick) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void EmptyStateCard() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void AddCreditCardDialog(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function6<? super java.lang.String, ? super java.lang.Double, ? super java.lang.Double, ? super java.lang.Double, ? super kotlinx.datetime.LocalDate, ? super java.lang.Double, kotlin.Unit> onAddCard) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void PaymentDialog(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.CreditCard creditCard, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.Double, kotlin.Unit> onMakePayment) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void PayoffPlannerDialog(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.CreditCard> creditCards, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToPlanner, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.DebtViewModel viewModel) {
    }
    
    private static final java.lang.String formatDate(kotlinx.datetime.LocalDate date) {
        return null;
    }
}