package com.focusflow.data.repository;

import com.focusflow.data.dao.CreditCardDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CreditCardRepository_Factory implements Factory<CreditCardRepository> {
  private final Provider<CreditCardDao> creditCardDaoProvider;

  public CreditCardRepository_Factory(Provider<CreditCardDao> creditCardDaoProvider) {
    this.creditCardDaoProvider = creditCardDaoProvider;
  }

  @Override
  public CreditCardRepository get() {
    return newInstance(creditCardDaoProvider.get());
  }

  public static CreditCardRepository_Factory create(Provider<CreditCardDao> creditCardDaoProvider) {
    return new CreditCardRepository_Factory(creditCardDaoProvider);
  }

  public static CreditCardRepository newInstance(CreditCardDao creditCardDao) {
    return new CreditCardRepository(creditCardDao);
  }
}
