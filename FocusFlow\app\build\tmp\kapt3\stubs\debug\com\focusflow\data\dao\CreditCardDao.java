package com.focusflow.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\u0005\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0014\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\r0\fH\'J\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\t0\rH\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u001c\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\r0\f2\u0006\u0010\u0011\u001a\u00020\u0012H\'J\u0018\u0010\u0013\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0010\u0010\u0014\u001a\u0004\u0018\u00010\u0015H\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u0018\u0010\u0016\u001a\u0004\u0018\u00010\u00152\u0006\u0010\u0011\u001a\u00020\u0012H\u00a7@\u00a2\u0006\u0002\u0010\u0017J\u0016\u0010\u0018\u001a\u00020\u00052\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u0019\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\n\u00a8\u0006\u001a"}, d2 = {"Lcom/focusflow/data/dao/CreditCardDao;", "", "deactivateCreditCard", "", "cardId", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteCreditCard", "creditCard", "Lcom/focusflow/data/model/CreditCard;", "(Lcom/focusflow/data/model/CreditCard;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllActiveCreditCards", "Lkotlinx/coroutines/flow/Flow;", "", "getAllCreditCardsSync", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCardsWithPaymentsDue", "date", "Lkotlinx/datetime/LocalDate;", "getCreditCardById", "getTotalDebt", "", "getTotalMinimumPaymentsDue", "(Lkotlinx/datetime/LocalDate;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertCreditCard", "updateCreditCard", "app_debug"})
@androidx.room.Dao
public abstract interface CreditCardDao {
    
    @androidx.room.Query(value = "SELECT * FROM credit_cards WHERE isActive = 1 ORDER BY dueDate ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.CreditCard>> getAllActiveCreditCards();
    
    @androidx.room.Query(value = "SELECT * FROM credit_cards WHERE id = :cardId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getCreditCardById(long cardId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.CreditCard> $completion);
    
    @androidx.room.Query(value = "SELECT SUM(currentBalance) FROM credit_cards WHERE isActive = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalDebt(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT SUM(minimumPayment) FROM credit_cards WHERE isActive = 1 AND dueDate <= :date")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalMinimumPaymentsDue(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate date, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM credit_cards WHERE isActive = 1 AND dueDate <= :date ORDER BY dueDate ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.CreditCard>> getCardsWithPaymentsDue(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate date);
    
    @androidx.room.Insert
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertCreditCard(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.CreditCard creditCard, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateCreditCard(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.CreditCard creditCard, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteCreditCard(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.CreditCard creditCard, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE credit_cards SET isActive = 0 WHERE id = :cardId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deactivateCreditCard(long cardId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM credit_cards WHERE isActive = 1 ORDER BY dueDate ASC")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAllCreditCardsSync(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.CreditCard>> $completion);
}